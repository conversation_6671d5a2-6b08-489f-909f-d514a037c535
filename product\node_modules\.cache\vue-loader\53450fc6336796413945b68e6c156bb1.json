{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755568755801}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div ref=\"mapChart\" class=\"map-chart\"></div>\n  </div>\n</template>\n\n<script>\nimport echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'MapComponent',\n  data () {\n    return {\n      chart: null,\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      geoJsonData: null\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n    window.addEventListener('resize', this.handleResize)\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    async initChart () {\n      console.log('初始化地图组件...')\n      if (!this.$refs.mapChart) {\n        console.error('地图容器未找到')\n        return\n      }\n\n      this.chart = echarts.init(this.$refs.mapChart)\n      console.log('ECharts实例创建成功')\n\n      // 加载青岛地理数据\n      try {\n        this.geoJsonData = require('./qingdao.json')\n        console.log('青岛地理数据加载成功')\n        echarts.registerMap('qingdao', this.geoJsonData)\n        console.log('青岛地图注册成功')\n      } catch (error) {\n        console.error('加载青岛地理数据失败:', error)\n        return\n      }\n\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: 'qingdao',\n          roam: false,\n          zoom: 1,\n          // center: [120.3826, 36.0671],\n          aspectScale: 0.7,\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '85%',\n          itemStyle: {\n            areaColor: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 120, 255, 0.3)' },\n                { offset: 0.5, color: 'rgba(0, 80, 200, 0.5)' },\n                { offset: 1, color: 'rgba(0, 40, 120, 0.7)' }\n              ]\n            },\n            borderColor: '#00D4FF',\n            borderWidth: 2,\n            shadowColor: 'rgba(0, 212, 255, 0.6)',\n            shadowBlur: 15,\n            shadowOffsetX: 0,\n            shadowOffsetY: 8,\n            opacity: 0.85\n          },\n          emphasis: {\n            itemStyle: {\n              areaColor: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(0, 180, 255, 0.6)' },\n                  { offset: 0.5, color: 'rgba(0, 140, 255, 0.7)' },\n                  { offset: 1, color: 'rgba(0, 100, 200, 0.8)' }\n                ]\n              },\n              borderColor: '#00FFFF',\n              borderWidth: 3,\n              shadowColor: 'rgba(0, 255, 255, 0.8)',\n              shadowBlur: 20,\n              shadowOffsetX: 0,\n              shadowOffsetY: 12,\n              opacity: 0.95\n            }\n          },\n          label: {\n            show: true,\n            color: '#E6F7FF',\n            fontSize: 11,\n            textShadowColor: 'rgba(0, 0, 0, 0.8)',\n            textShadowBlur: 4,\n            textShadowOffsetX: 1,\n            textShadowOffsetY: 1\n          }\n        },\n        series: [\n          {\n            type: 'map',\n            map: 'qingdao',\n            data: this.mapData,\n            geoIndex: 0,\n            itemStyle: {\n              opacity: 0\n            },\n            emphasis: {\n              itemStyle: {\n                opacity: 0\n              }\n            },\n            label: {\n              show: false\n            }\n          },\n          // 添加散点图显示数据\n          {\n            type: 'scatter',\n            coordinateSystem: 'geo',\n            data: this.mapData.map(item => ({\n              name: item.name,\n              value: item.value,\n              itemStyle: {\n                color: '#FFD600',\n                shadowColor: 'rgba(255, 214, 0, 0.8)',\n                shadowBlur: 10\n              }\n            })),\n            symbolSize: function (val) {\n              return Math.max(val[1] / 100, 8)\n            },\n            label: {\n              show: true,\n              formatter: '{b}\\n{c}',\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 10,\n              fontWeight: 'bold',\n              textShadowColor: 'rgba(0, 0, 0, 0.8)',\n              textShadowBlur: 2\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 12,\n                color: '#FFD600'\n              }\n            }\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        console.log('地图点击事件:', params)\n        if (params.componentType === 'series') {\n          this.handleRegionClick({\n            name: params.name,\n            value: params.value,\n            type: 'region'\n          })\n        }\n      })\n    },\n\n    handleRegionClick (region) {\n      this.$emit('region-click', region)\n      console.log('点击地区:', region.name, '数值:', region.value)\n    },\n\n    handleResize () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}