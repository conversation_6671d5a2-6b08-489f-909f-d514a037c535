{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755574610613}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 3,\n            borderColor: '#00D4FF',\n            shadowColor: 'rgba(0, 212, 255, 0.8)',\n            shadowBlur: 15,\n            shadowOffsetX: 0,\n            shadowOffsetY: 8\n          },\n          emphasis: {\n            borderWidth: 4,\n            borderColor: '#FFFFFF',\n            shadowColor: 'rgba(255, 255, 255, 0.9)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 12\n          }\n        },\n        series: []\n      },\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n      // const option = {\n\n      //   geo: {\n      //     map: '智慧人大',\n      //     layoutCenter: ['50%', '50%'],\n      //     layoutSize: '90%',\n      //     roam: false,\n      //     zoom: 1,\n\n      //     label: {\n      //       show: true,\n      //       color: '#E6F7FF',\n      //       fontSize: 11,\n      //       textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //       textShadowBlur: 4,\n      //       textShadowOffsetX: 1,\n      //       textShadowOffsetY: 1\n      //     }\n      //   },\n      //   series: [\n      //     {\n      //       type: 'map',\n      //       map: 'qingdao',\n      //       data: this.data,\n      //       geoIndex: 0,\n      //       itemStyle: {\n      //         opacity: 0\n      //       },\n      //       emphasis: {\n      //         itemStyle: {\n      //           opacity: 0\n      //         }\n      //       },\n      //       label: {\n      //         show: false\n      //       }\n      //     },\n      //     // 添加散点图显示数据\n      //     {\n      //       type: 'scatter',\n      //       coordinateSystem: 'geo',\n      //       data: this.data.map(item => ({\n      //         name: item.name,\n      //         value: item.value,\n      //         itemStyle: {\n      //           color: '#FFD600',\n      //           shadowColor: 'rgba(255, 214, 0, 0.6)',\n      //           shadowBlur: 8,\n      //           borderColor: '#FFFFFF',\n      //           borderWidth: 1\n      //         }\n      //       })),\n      //       symbolSize: function (val) {\n      //         return Math.max(val[1] / 100, 8)\n      //       },\n      //       label: {\n      //         show: true,\n      //         formatter: '{b}\\n{c}',\n      //         position: 'top',\n      //         color: '#FFFFFF',\n      //         fontSize: 10,\n      //         fontWeight: 'bold',\n      //         textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //         textShadowBlur: 2\n      //       },\n      //       emphasis: {\n      //         label: {\n      //           show: true,\n      //           fontSize: 12,\n      //           color: '#FFD600'\n      //         },\n      //         itemStyle: {\n      //           shadowBlur: 12,\n      //           shadowColor: 'rgba(255, 214, 0, 0.8)'\n      //         }\n      //       }\n      //     }\n      //   ]\n      // }\n      // this.chart.setOption(option)\n      // // 添加点击事件\n      // this.chart.on('click', (params) => {\n      //   console.log('地图点击事件:', params)\n      //   if (params.componentType === 'series') {\n      //     this.handleRegionClick({\n      //       name: params.name,\n      //       value: params.value,\n      //       type: 'region'\n      //     })\n      //   }\n      // })\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 16,\n          position: 'center',\n          color: '#FFFFFF',\n          fontWeight: 'bold',\n          textShadowColor: 'rgba(0, 0, 0, 0.8)',\n          textShadowBlur: 8,\n          textShadowOffsetX: 2,\n          textShadowOffsetY: 2,\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            fontSize: 18,\n            color: '#00FFFF',\n            textShadowColor: 'rgba(0, 255, 255, 0.8)',\n            textShadowBlur: 12\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#00D4FF',\n          borderWidth: 3,\n          shadowColor: 'rgba(0, 212, 255, 1)',\n          shadowBlur: 20,\n          shadowOffsetX: 0,\n          shadowOffsetY: 0,\n          areaColor: {\n            type: 'radial',\n            x: 0.3,\n            y: 0.2,\n            r: 1.2,\n            colorStops: [\n              { offset: 0, color: 'rgba(0, 255, 255, 0.4)' },\n              { offset: 0.3, color: 'rgba(0, 180, 255, 0.6)' },\n              { offset: 0.7, color: 'rgba(30, 120, 200, 0.8)' },\n              { offset: 1, color: 'rgba(10, 29, 76, 0.9)' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#FFFFFF',\n            shadowColor: 'rgba(255, 255, 255, 1)',\n            shadowBlur: 35,\n            shadowOffsetX: 0,\n            shadowOffsetY: 0,\n            borderWidth: 4,\n            areaColor: {\n              type: 'radial',\n              x: 0.3,\n              y: 0.2,\n              r: 1.2,\n              colorStops: [\n                { offset: 0, color: 'rgba(255, 255, 255, 0.3)' },\n                { offset: 0.2, color: 'rgba(0, 255, 255, 0.7)' },\n                { offset: 0.6, color: 'rgba(0, 200, 255, 0.8)' },\n                { offset: 1, color: 'rgba(0, 100, 200, 0.9)' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}