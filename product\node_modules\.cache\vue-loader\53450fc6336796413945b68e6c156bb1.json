{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755569068527}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAaA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <!-- 背景放射光线效果 -->\n    <div class=\"background-rays\">\n      <div class=\"ray\" v-for=\"i in 24\" :key=\"i\" :style=\"{ transform: `rotate(${i * 15}deg)` }\"></div>\n    </div>\n    <!-- 圆形扫描线效果 -->\n    <div class=\"scan-circle\"></div>\n    <div ref=\"mapChart\" class=\"map-chart\"></div>\n  </div>\n</template>\n\n<script>\nimport echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'MapComponent',\n  data () {\n    return {\n      chart: null,\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      geoJsonData: null\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n    window.addEventListener('resize', this.handleResize)\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    async initChart () {\n      console.log('初始化地图组件...')\n      if (!this.$refs.mapChart) {\n        console.error('地图容器未找到')\n        return\n      }\n\n      this.chart = echarts.init(this.$refs.mapChart)\n      console.log('ECharts实例创建成功')\n\n      // 加载青岛地理数据\n      try {\n        this.geoJsonData = require('./qingdao.json')\n        console.log('青岛地理数据加载成功')\n        echarts.registerMap('qingdao', this.geoJsonData)\n        console.log('青岛地图注册成功')\n      } catch (error) {\n        console.error('加载青岛地理数据失败:', error)\n        return\n      }\n\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: 'qingdao',\n          roam: false,\n          zoom: 1,\n          // center: [120.3826, 36.0671],\n          aspectScale: 0.7,\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '85%',\n          itemStyle: {\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.5,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 255, 255, 0.4)' },\n                { offset: 0.3, color: 'rgba(0, 180, 255, 0.6)' },\n                { offset: 0.6, color: 'rgba(0, 120, 200, 0.7)' },\n                { offset: 1, color: 'rgba(0, 60, 120, 0.9)' }\n              ]\n            },\n            borderColor: '#00FFFF',\n            borderWidth: 3,\n            shadowColor: 'rgba(0, 255, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 15,\n            opacity: 0.9\n          },\n          emphasis: {\n            itemStyle: {\n              areaColor: {\n                type: 'radial',\n                x: 0.5,\n                y: 0.5,\n                r: 0.8,\n                colorStops: [\n                  { offset: 0, color: 'rgba(255, 255, 255, 0.3)' },\n                  { offset: 0.2, color: 'rgba(0, 255, 255, 0.7)' },\n                  { offset: 0.5, color: 'rgba(0, 200, 255, 0.8)' },\n                  { offset: 1, color: 'rgba(0, 100, 200, 0.9)' }\n                ]\n              },\n              borderColor: '#FFFFFF',\n              borderWidth: 4,\n              shadowColor: 'rgba(255, 255, 255, 1)',\n              shadowBlur: 30,\n              shadowOffsetX: 0,\n              shadowOffsetY: 20,\n              opacity: 1\n            }\n          },\n          label: {\n            show: true,\n            color: '#E6F7FF',\n            fontSize: 11,\n            textShadowColor: 'rgba(0, 0, 0, 0.8)',\n            textShadowBlur: 4,\n            textShadowOffsetX: 1,\n            textShadowOffsetY: 1\n          }\n        },\n        series: [\n          {\n            type: 'map',\n            map: 'qingdao',\n            data: this.mapData,\n            geoIndex: 0,\n            itemStyle: {\n              opacity: 0\n            },\n            emphasis: {\n              itemStyle: {\n                opacity: 0\n              }\n            },\n            label: {\n              show: false\n            }\n          },\n          // 添加发光边界效果\n          {\n            type: 'lines',\n            coordinateSystem: 'geo',\n            data: [],\n            lineStyle: {\n              color: '#00FFFF',\n              width: 2,\n              opacity: 0.8,\n              shadowColor: 'rgba(0, 255, 255, 0.8)',\n              shadowBlur: 10\n            },\n            effect: {\n              show: true,\n              period: 3,\n              trailLength: 0.1,\n              color: '#FFFFFF',\n              symbolSize: 3\n            }\n          },\n          // 添加散点图显示数据\n          {\n            type: 'scatter',\n            coordinateSystem: 'geo',\n            data: this.mapData.map(item => ({\n              name: item.name,\n              value: item.value,\n              itemStyle: {\n                color: {\n                  type: 'radial',\n                  x: 0.5,\n                  y: 0.5,\n                  r: 0.5,\n                  colorStops: [\n                    { offset: 0, color: '#FFFFFF' },\n                    { offset: 0.5, color: '#00FFFF' },\n                    { offset: 1, color: '#0080FF' }\n                  ]\n                },\n                shadowColor: 'rgba(0, 255, 255, 1)',\n                shadowBlur: 15,\n                borderColor: '#FFFFFF',\n                borderWidth: 2\n              }\n            })),\n            symbolSize: function (val) {\n              return Math.max(val[1] / 80, 12)\n            },\n            label: {\n              show: true,\n              formatter: '{b}\\n{c}',\n              position: 'top',\n              color: '#E6F7FF',\n              fontSize: 11,\n              fontWeight: 'bold',\n              textShadowColor: 'rgba(0, 0, 0, 0.9)',\n              textShadowBlur: 3,\n              textBorderColor: 'rgba(0, 255, 255, 0.5)',\n              textBorderWidth: 1\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 13,\n                color: '#FFFFFF'\n              },\n              itemStyle: {\n                shadowBlur: 25,\n                shadowColor: 'rgba(255, 255, 255, 1)'\n              }\n            }\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        console.log('地图点击事件:', params)\n        if (params.componentType === 'series') {\n          this.handleRegionClick({\n            name: params.name,\n            value: params.value,\n            type: 'region'\n          })\n        }\n      })\n    },\n\n    handleRegionClick (region) {\n      this.$emit('region-click', region)\n      console.log('点击地区:', region.name, '数值:', region.value)\n    },\n\n    handleResize () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: radial-gradient(circle at center, rgba(0, 40, 80, 0.8) 0%, rgba(0, 20, 40, 0.95) 70%, rgba(0, 10, 20, 1) 100%);\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n/* 背景放射光线效果 */\n.background-rays {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 100%;\n  height: 100%;\n  transform: translate(-50%, -50%);\n  z-index: 1;\n  pointer-events: none;\n}\n\n.ray {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 2px;\n  height: 50%;\n  background: linear-gradient(to bottom,\n      rgba(0, 255, 255, 0.3) 0%,\n      rgba(0, 200, 255, 0.2) 30%,\n      rgba(0, 150, 255, 0.1) 60%,\n      transparent 100%);\n  transform-origin: 0 0;\n  animation: rayPulse 4s ease-in-out infinite;\n}\n\n.ray:nth-child(odd) {\n  animation-delay: 0.5s;\n}\n\n.ray:nth-child(3n) {\n  animation-delay: 1s;\n}\n\n/* 圆形扫描线效果 */\n.scan-circle {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 80%;\n  height: 80%;\n  border: 2px solid transparent;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 2;\n  pointer-events: none;\n  background: conic-gradient(from 0deg,\n      transparent 0deg,\n      rgba(0, 255, 255, 0.3) 30deg,\n      rgba(0, 255, 255, 0.6) 60deg,\n      rgba(0, 255, 255, 0.3) 90deg,\n      transparent 120deg,\n      transparent 360deg);\n  animation: scanRotate 8s linear infinite;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes rayPulse {\n\n  0%,\n  100% {\n    opacity: 0.3;\n    transform: scaleY(1);\n  }\n\n  50% {\n    opacity: 0.8;\n    transform: scaleY(1.2);\n  }\n}\n\n@keyframes scanRotate {\n  0% {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n\n  100% {\n    transform: translate(-50%, -50%) rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}