{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755574428620}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2, // 设置外层边框\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0, // 阴影水平方向上的偏移距离。\n            shadowOffsetY: 20\n          },\n          emphasis: {\n            borderWidth: 2, // 设置外层边框\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 20, // 阴影水平方向上的偏移距离。\n            shadowOffsetY: 10\n          }\n        },\n        series: []\n      },\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n      // const option = {\n\n      //   geo: {\n      //     map: '智慧人大',\n      //     layoutCenter: ['50%', '50%'],\n      //     layoutSize: '90%',\n      //     roam: false,\n      //     zoom: 1,\n\n      //     label: {\n      //       show: true,\n      //       color: '#E6F7FF',\n      //       fontSize: 11,\n      //       textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //       textShadowBlur: 4,\n      //       textShadowOffsetX: 1,\n      //       textShadowOffsetY: 1\n      //     }\n      //   },\n      //   series: [\n      //     {\n      //       type: 'map',\n      //       map: 'qingdao',\n      //       data: this.data,\n      //       geoIndex: 0,\n      //       itemStyle: {\n      //         opacity: 0\n      //       },\n      //       emphasis: {\n      //         itemStyle: {\n      //           opacity: 0\n      //         }\n      //       },\n      //       label: {\n      //         show: false\n      //       }\n      //     },\n      //     // 添加散点图显示数据\n      //     {\n      //       type: 'scatter',\n      //       coordinateSystem: 'geo',\n      //       data: this.data.map(item => ({\n      //         name: item.name,\n      //         value: item.value,\n      //         itemStyle: {\n      //           color: '#FFD600',\n      //           shadowColor: 'rgba(255, 214, 0, 0.6)',\n      //           shadowBlur: 8,\n      //           borderColor: '#FFFFFF',\n      //           borderWidth: 1\n      //         }\n      //       })),\n      //       symbolSize: function (val) {\n      //         return Math.max(val[1] / 100, 8)\n      //       },\n      //       label: {\n      //         show: true,\n      //         formatter: '{b}\\n{c}',\n      //         position: 'top',\n      //         color: '#FFFFFF',\n      //         fontSize: 10,\n      //         fontWeight: 'bold',\n      //         textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //         textShadowBlur: 2\n      //       },\n      //       emphasis: {\n      //         label: {\n      //           show: true,\n      //           fontSize: 12,\n      //           color: '#FFD600'\n      //         },\n      //         itemStyle: {\n      //           shadowBlur: 12,\n      //           shadowColor: 'rgba(255, 214, 0, 0.8)'\n      //         }\n      //       }\n      //     }\n      //   ]\n      // }\n      // this.chart.setOption(option)\n      // // 添加点击事件\n      // this.chart.on('click', (params) => {\n      //   console.log('地图点击事件:', params)\n      //   if (params.componentType === 'series') {\n      //     this.handleRegionClick({\n      //       name: params.name,\n      //       value: params.value,\n      //       type: 'region'\n      //     })\n      //   }\n      // })\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 18,\n          position: 'center',\n          color: '#fff',\n          textShadowColor: '#00eaff',\n          textShadowBlur: 10,\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#ffffff',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.8)',\n          shadowBlur: 10,\n          shadowOffsetX: 0,\n          shadowOffsetY: 0,\n          areaColor: {\n            type: 'radial',\n            x: 0.5,\n            y: 0.1,\n            r: 0.9,\n            colorStops: [\n              { offset: 0, color: '#0a1d4c' },\n              { offset: 1, color: '#2176ff' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00eaff',\n            shadowColor: 'rgba(0,234,255,0.8)',\n            shadowBlur: 30,\n            shadowOffsetX: 0,\n            shadowOffsetY: 0,\n            borderWidth: 2,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.5,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: '#2176ff' },\n                { offset: 1, color: '#0a1d4c' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}