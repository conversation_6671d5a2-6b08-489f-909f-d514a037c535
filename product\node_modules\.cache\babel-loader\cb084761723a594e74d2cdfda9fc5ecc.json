{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755568755801}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAOA;AACA;AAEA;EACAA,oBADA;;EAEAC;IACA;MACAC,WADA;MAEAC,UACA;QAAAH;QAAAI;MAAA,CADA,EAEA;QAAAJ;QAAAI;MAAA,CAFA,EAGA;QAAAJ;QAAAI;MAAA,CAHA,EAIA;QAAAJ;QAAAI;MAAA,CAJA,EAKA;QAAAJ;QAAAI;MAAA,CALA,EAMA;QAAAJ;QAAAI;MAAA,CANA,EAOA;QAAAJ;QAAAI;MAAA,CAPA,EAQA;QAAAJ;QAAAI;MAAA,CARA,EASA;QAAAJ;QAAAI;MAAA,CATA,EAUA;QAAAJ;QAAAI;MAAA,CAVA,CAFA;MAcAC;IAdA;EAgBA,CAnBA;;EAoBAC;IACA;MACA;IACA,CAFA;IAGAC;EACA,CAzBA;;EA0BAC;IACA;MACA;IACA;;IACAD;EACA,CA/BA;;EAgCAE;IACA;MACAC;;MACA;QACAA;QACA;MACA;;MAEA;MACAA,6BARA,CAUA;;MACA;QACA;QACAA;QACAC;QACAD;MACA,CALA,CAKA;QACAA;QACA;MACA;;MAEA;QACAE;UACAC,eADA;UAEAC;YACA;AACA;AACA;AACA,mBAHA;UAIA,CAPA;UAQAC,wCARA;UASAC,sBATA;UAUAC,cAVA;UAWAC,eAXA;UAYAC,qCAZA;UAaAC,cAbA;UAcAC;YACAC,gBADA;YAEAC,YAFA;YAGAC;UAHA;QAdA,CADA;QAqBAC;UACAC,cADA;UAEAC,WAFA;UAGAC,OAHA;UAIA;UACAC,gBALA;UAMAC,4BANA;UAOAC,iBAPA;UAQAC;YACAC;cACAC,cADA;cAEAC,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAAlB;cAAA,CADA,EAEA;gBAAAkB;gBAAAlB;cAAA,CAFA,EAGA;gBAAAkB;gBAAAlB;cAAA,CAHA;YANA,CADA;YAaAN,sBAbA;YAcAC,cAdA;YAeAE,qCAfA;YAgBAC,cAhBA;YAiBAqB,gBAjBA;YAkBAC,gBAlBA;YAmBAC;UAnBA,CARA;UA6BAC;YACAZ;cACAC;gBACAC,cADA;gBAEAC,IAFA;gBAGAC,IAHA;gBAIAC,KAJA;gBAKAC,KALA;gBAMAC,aACA;kBAAAC;kBAAAlB;gBAAA,CADA,EAEA;kBAAAkB;kBAAAlB;gBAAA,CAFA,EAGA;kBAAAkB;kBAAAlB;gBAAA,CAHA;cANA,CADA;cAaAN,sBAbA;cAcAC,cAdA;cAeAE,qCAfA;cAgBAC,cAhBA;cAiBAqB,gBAjBA;cAkBAC,iBAlBA;cAmBAC;YAnBA;UADA,CA7BA;UAoDAE;YACAC,UADA;YAEAxB,gBAFA;YAGAC,YAHA;YAIAwB,qCAJA;YAKAC,iBALA;YAMAC,oBANA;YAOAC;UAPA;QApDA,CArBA;QAmFAC,SACA;UACAjB,WADA;UAEAR,cAFA;UAGAzB,kBAHA;UAIAmD,WAJA;UAKApB;YACAW;UADA,CALA;UAQAC;YACAZ;cACAW;YADA;UADA,CARA;UAaAE;YACAC;UADA;QAbA,CADA,EAkBA;QACA;UACAZ,eADA;UAEAmB,uBAFA;UAGApD;YACAD,eADA;YAEAI,iBAFA;YAGA4B;cACAV,gBADA;cAEAH,qCAFA;cAGAC;YAHA;UAHA,GAHA;UAYAkC;YACA;UACA,CAdA;UAeAT;YACAC,UADA;YAEAhC,qBAFA;YAGAyC,eAHA;YAIAjC,gBAJA;YAKAC,YALA;YAMAiC,kBANA;YAOAT,qCAPA;YAQAC;UARA,CAfA;UAyBAJ;YACAC;cACAC,UADA;cAEAvB,YAFA;cAGAD;YAHA;UADA;QAzBA,CAnBA;MAnFA;MAyIA,6BA9JA,CA+JA;;MACA;QACAZ;;QACA;UACA;YACAV,iBADA;YAEAI,mBAFA;YAGA8B;UAHA;QAKA;MACA,CATA;IAUA,CA3KA;;IA6KAuB;MACA;MACA/C;IACA,CAhLA;;IAkLAgD;MACA;QACA;MACA;IACA;;EAtLA;AAhCA", "names": ["name", "data", "chart", "mapData", "value", "geoJsonData", "mounted", "window", "<PERSON><PERSON><PERSON><PERSON>", "methods", "console", "echarts", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textStyle", "color", "fontSize", "fontFamily", "geo", "map", "roam", "zoom", "aspectScale", "layoutCenter", "layoutSize", "itemStyle", "areaColor", "type", "x", "y", "x2", "y2", "colorStops", "offset", "shadowOffsetX", "shadowOffsetY", "opacity", "emphasis", "label", "show", "textShadowColor", "textShadowBlur", "textShadowOffsetX", "textShadowOffsetY", "series", "geoIndex", "coordinateSystem", "symbolSize", "position", "fontWeight", "handleRegionClick", "handleResize"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["MapComponent.vue"], "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div ref=\"mapChart\" class=\"map-chart\"></div>\n  </div>\n</template>\n\n<script>\nimport echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'MapComponent',\n  data () {\n    return {\n      chart: null,\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      geoJsonData: null\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n    window.addEventListener('resize', this.handleResize)\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    async initChart () {\n      console.log('初始化地图组件...')\n      if (!this.$refs.mapChart) {\n        console.error('地图容器未找到')\n        return\n      }\n\n      this.chart = echarts.init(this.$refs.mapChart)\n      console.log('ECharts实例创建成功')\n\n      // 加载青岛地理数据\n      try {\n        this.geoJsonData = require('./qingdao.json')\n        console.log('青岛地理数据加载成功')\n        echarts.registerMap('qingdao', this.geoJsonData)\n        console.log('青岛地图注册成功')\n      } catch (error) {\n        console.error('加载青岛地理数据失败:', error)\n        return\n      }\n\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: 'qingdao',\n          roam: false,\n          zoom: 1,\n          // center: [120.3826, 36.0671],\n          aspectScale: 0.7,\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '85%',\n          itemStyle: {\n            areaColor: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 120, 255, 0.3)' },\n                { offset: 0.5, color: 'rgba(0, 80, 200, 0.5)' },\n                { offset: 1, color: 'rgba(0, 40, 120, 0.7)' }\n              ]\n            },\n            borderColor: '#00D4FF',\n            borderWidth: 2,\n            shadowColor: 'rgba(0, 212, 255, 0.6)',\n            shadowBlur: 15,\n            shadowOffsetX: 0,\n            shadowOffsetY: 8,\n            opacity: 0.85\n          },\n          emphasis: {\n            itemStyle: {\n              areaColor: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: 'rgba(0, 180, 255, 0.6)' },\n                  { offset: 0.5, color: 'rgba(0, 140, 255, 0.7)' },\n                  { offset: 1, color: 'rgba(0, 100, 200, 0.8)' }\n                ]\n              },\n              borderColor: '#00FFFF',\n              borderWidth: 3,\n              shadowColor: 'rgba(0, 255, 255, 0.8)',\n              shadowBlur: 20,\n              shadowOffsetX: 0,\n              shadowOffsetY: 12,\n              opacity: 0.95\n            }\n          },\n          label: {\n            show: true,\n            color: '#E6F7FF',\n            fontSize: 11,\n            textShadowColor: 'rgba(0, 0, 0, 0.8)',\n            textShadowBlur: 4,\n            textShadowOffsetX: 1,\n            textShadowOffsetY: 1\n          }\n        },\n        series: [\n          {\n            type: 'map',\n            map: 'qingdao',\n            data: this.mapData,\n            geoIndex: 0,\n            itemStyle: {\n              opacity: 0\n            },\n            emphasis: {\n              itemStyle: {\n                opacity: 0\n              }\n            },\n            label: {\n              show: false\n            }\n          },\n          // 添加散点图显示数据\n          {\n            type: 'scatter',\n            coordinateSystem: 'geo',\n            data: this.mapData.map(item => ({\n              name: item.name,\n              value: item.value,\n              itemStyle: {\n                color: '#FFD600',\n                shadowColor: 'rgba(255, 214, 0, 0.8)',\n                shadowBlur: 10\n              }\n            })),\n            symbolSize: function (val) {\n              return Math.max(val[1] / 100, 8)\n            },\n            label: {\n              show: true,\n              formatter: '{b}\\n{c}',\n              position: 'top',\n              color: '#FFFFFF',\n              fontSize: 10,\n              fontWeight: 'bold',\n              textShadowColor: 'rgba(0, 0, 0, 0.8)',\n              textShadowBlur: 2\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 12,\n                color: '#FFD600'\n              }\n            }\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        console.log('地图点击事件:', params)\n        if (params.componentType === 'series') {\n          this.handleRegionClick({\n            name: params.name,\n            value: params.value,\n            type: 'region'\n          })\n        }\n      })\n    },\n\n    handleRegionClick (region) {\n      this.$emit('region-click', region)\n      console.log('点击地区:', region.name, '数值:', region.value)\n    },\n\n    handleResize () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}