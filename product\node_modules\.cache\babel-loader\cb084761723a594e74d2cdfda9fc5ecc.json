{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755574428620}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAOA;;AACAA,kC,CAAA;;;AAEA;EACAC,oBADA;EAEAC,qCAFA;;EAGAC;IACA;MACAC;QACAC;UACAC,eADA;UAEAC;YACA;AACA;AACA;AACA,mBAHA;UAIA,CAPA;UAQAC,wCARA;UASAC,sBATA;UAUAC,cAVA;UAWAC,eAXA;UAYAC,qCAZA;UAaAC,cAbA;UAcAC;YACAC,gBADA;YAEAC,YAFA;YAGAC;UAHA;QAdA,CADA;QAqBAC;UACAC,WADA;UAEAC,4BAFA;UAGAC,iBAHA;UAIAC,WAJA;UAKAC;YACAb,cADA;YACA;YACAD,mCAFA;YAGAG,mCAHA;YAIAC,aAJA;YAKAW,gBALA;YAKA;YACAC;UANA,CALA;UAaAC;YACAhB,cADA;YACA;YACAD,mCAFA;YAGAG,oCAHA;YAIAC,aAJA;YAKAW,iBALA;YAKA;YACAC;UANA;QAbA,CArBA;QA2CAE;MA3CA,CADA;MA8CAC,UACA;QAAA3B;QAAA4B;MAAA,CADA,EAEA;QAAA5B;QAAA4B;MAAA,CAFA,EAGA;QAAA5B;QAAA4B;MAAA,CAHA,EAIA;QAAA5B;QAAA4B;MAAA,CAJA,EAKA;QAAA5B;QAAA4B;MAAA,CALA,EAMA;QAAA5B;QAAA4B;MAAA,CANA,EAOA;QAAA5B;QAAA4B;MAAA,CAPA,EAQA;QAAA5B;QAAA4B;MAAA,CARA,EASA;QAAA5B;QAAA4B;MAAA,CATA,EAUA;QAAA5B;QAAA4B;MAAA,CAVA,CA9CA;MA0DAC,kBA1DA;MA2DAC;IA3DA;EA6DA,CAjEA;;EAkEAC;IACA;MACA;IACA,CAFA;EAGA,CAtEA;;EAuEAC;IACAC;MACA;MACA;IACA,CAJA;;IAKA;MACA;MACA;;MACA;QACA;;QACA;UACA;;UACA;YACAC;cAAAC;YAAA;UACA;QACA;MACA;;MACA,4BAZA,CAaA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA7GA;;IA+GAC;MACA;MACA;MACAC;MACA;MACA;MACAC;MACAC,sBACA,0BADA;QAEAb;MAFA;MAIAa;MACAA;QAAA;;QACA;QACA,+HAFA,CAGA;;QACAA;UACAb,SACA,oBADA,EAEA,0EAFA;QADA;MAMA,CAVA;MAWAc;QACA;UACAD;QACA;MACA,CAJA;IAKA,CA3IA;;IA6IAE;MACA;MACA;MACA;IACA,CAjJA;;IAmJAC;MACA;QACAC,eADA;QAEAC,uBAFA;QAGA1C,eAHA;QAIA2C,UAJA;QAKAC,oBALA;QAMAC,WANA;QAOAC;MAPA;IASA,CA7JA;;IA+JAC;MACA;QACA9B,4BADA;QAEAC,iBAFA;QAGApB,WAHA;QAIA2C,WAJA;QAKAzB,WALA;QAMA6B,WANA;QAOAG,uBAPA;QAQAC;UACAN,UADA;UAEA9B,YAFA;UAGAqC,kBAHA;UAIAtC,aAJA;UAKAuC,0BALA;UAMAC,kBANA;UAOAhD;YACA;UACA,CATA;UAUAmB;YACAoB,UADA;YAEAhC;cAAAC;YAAA;UAFA;QAVA,CARA;QAuBAO,WAvBA;QAwBAC;UACAd,sBADA;UAEAC,cAFA;UAGAE,qCAHA;UAIAC,cAJA;UAKAW,gBALA;UAMAC,gBANA;UAOA+B;YACAZ,cADA;YAEAa,MAFA;YAGAC,MAHA;YAIAC,MAJA;YAKAC,aACA;cAAAC;cAAA9C;YAAA,CADA,EAEA;cAAA8C;cAAA9C;YAAA,CAFA;UALA;QAPA,CAxBA;QA0CAW;UACAH;YACAd,sBADA;YAEAG,kCAFA;YAGAC,cAHA;YAIAW,gBAJA;YAKAC,gBALA;YAMAf,cANA;YAOA8C;cACAZ,cADA;cAEAa,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAA9C;cAAA,CADA,EAEA;gBAAA8C;gBAAA9C;cAAA,CAFA;YALA;UAPA;QADA;MA1CA;IA+DA;;EA/NA;AAvEA", "names": ["require", "name", "props", "data", "options", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textStyle", "color", "fontSize", "fontFamily", "geo", "map", "layoutCenter", "layoutSize", "roam", "itemStyle", "shadowOffsetX", "shadowOffsetY", "emphasis", "series", "mapData", "value", "echartObjRef", "regionList", "mounted", "methods", "splitFileName", "geoJson", "features", "renderMap", "dom", "echarts", "echartObj", "window", "getOptions", "getSeriesData", "type", "coordinateSystem", "show", "symbolSize", "geoIndex", "symbolOffset", "getMapSeries", "showLegendSymbol", "label", "position", "textShadowColor", "textShadowBlur", "areaColor", "x", "y", "r", "colorStops", "offset"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["MapComponent.vue"], "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2, // 设置外层边框\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0, // 阴影水平方向上的偏移距离。\n            shadowOffsetY: 20\n          },\n          emphasis: {\n            borderWidth: 2, // 设置外层边框\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 20, // 阴影水平方向上的偏移距离。\n            shadowOffsetY: 10\n          }\n        },\n        series: []\n      },\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n      // const option = {\n\n      //   geo: {\n      //     map: '智慧人大',\n      //     layoutCenter: ['50%', '50%'],\n      //     layoutSize: '90%',\n      //     roam: false,\n      //     zoom: 1,\n\n      //     label: {\n      //       show: true,\n      //       color: '#E6F7FF',\n      //       fontSize: 11,\n      //       textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //       textShadowBlur: 4,\n      //       textShadowOffsetX: 1,\n      //       textShadowOffsetY: 1\n      //     }\n      //   },\n      //   series: [\n      //     {\n      //       type: 'map',\n      //       map: 'qingdao',\n      //       data: this.data,\n      //       geoIndex: 0,\n      //       itemStyle: {\n      //         opacity: 0\n      //       },\n      //       emphasis: {\n      //         itemStyle: {\n      //           opacity: 0\n      //         }\n      //       },\n      //       label: {\n      //         show: false\n      //       }\n      //     },\n      //     // 添加散点图显示数据\n      //     {\n      //       type: 'scatter',\n      //       coordinateSystem: 'geo',\n      //       data: this.data.map(item => ({\n      //         name: item.name,\n      //         value: item.value,\n      //         itemStyle: {\n      //           color: '#FFD600',\n      //           shadowColor: 'rgba(255, 214, 0, 0.6)',\n      //           shadowBlur: 8,\n      //           borderColor: '#FFFFFF',\n      //           borderWidth: 1\n      //         }\n      //       })),\n      //       symbolSize: function (val) {\n      //         return Math.max(val[1] / 100, 8)\n      //       },\n      //       label: {\n      //         show: true,\n      //         formatter: '{b}\\n{c}',\n      //         position: 'top',\n      //         color: '#FFFFFF',\n      //         fontSize: 10,\n      //         fontWeight: 'bold',\n      //         textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //         textShadowBlur: 2\n      //       },\n      //       emphasis: {\n      //         label: {\n      //           show: true,\n      //           fontSize: 12,\n      //           color: '#FFD600'\n      //         },\n      //         itemStyle: {\n      //           shadowBlur: 12,\n      //           shadowColor: 'rgba(255, 214, 0, 0.8)'\n      //         }\n      //       }\n      //     }\n      //   ]\n      // }\n      // this.chart.setOption(option)\n      // // 添加点击事件\n      // this.chart.on('click', (params) => {\n      //   console.log('地图点击事件:', params)\n      //   if (params.componentType === 'series') {\n      //     this.handleRegionClick({\n      //       name: params.name,\n      //       value: params.value,\n      //       type: 'region'\n      //     })\n      //   }\n      // })\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 18,\n          position: 'center',\n          color: '#fff',\n          textShadowColor: '#00eaff',\n          textShadowBlur: 10,\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#ffffff',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.8)',\n          shadowBlur: 10,\n          shadowOffsetX: 0,\n          shadowOffsetY: 0,\n          areaColor: {\n            type: 'radial',\n            x: 0.5,\n            y: 0.1,\n            r: 0.9,\n            colorStops: [\n              { offset: 0, color: '#0a1d4c' },\n              { offset: 1, color: '#2176ff' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00eaff',\n            shadowColor: 'rgba(0,234,255,0.8)',\n            shadowBlur: 30,\n            shadowOffsetX: 0,\n            shadowOffsetY: 0,\n            borderWidth: 2,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.5,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: '#2176ff' },\n                { offset: 1, color: '#0a1d4c' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}