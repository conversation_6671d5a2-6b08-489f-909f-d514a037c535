{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755569068527}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGVjaGFydHMgZnJvbSAnZWNoYXJ0cyc7CmltcG9ydCAnZWNoYXJ0cy1nbCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWFwQ29tcG9uZW50JywKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGNoYXJ0OiBudWxsLAogICAgICBtYXBEYXRhOiBbewogICAgICAgIG5hbWU6ICfluILljZfljLonLAogICAgICAgIHZhbHVlOiAxMjAwCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5biC5YyX5Yy6JywKICAgICAgICB2YWx1ZTogMTMwMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+m7hOWym+WMuicsCiAgICAgICAgdmFsdWU6IDg1MAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+W0guWxseWMuicsCiAgICAgICAgdmFsdWU6IDcwMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+adjuayp+WMuicsCiAgICAgICAgdmFsdWU6IDEwMDAKICAgICAgfSwgewogICAgICAgIG5hbWU6ICfln47pmLPljLonLAogICAgICAgIHZhbHVlOiAxMTAwCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5Y2z5aKo5Yy6JywKICAgICAgICB2YWx1ZTogOTUwCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn6IO25bee5biCJywKICAgICAgICB2YWx1ZTogODAwCiAgICAgIH0sIHsKICAgICAgICBuYW1lOiAn5bmz5bqm5biCJywKICAgICAgICB2YWx1ZTogMTQwMAogICAgICB9LCB7CiAgICAgICAgbmFtZTogJ+iOseilv+W4gicsCiAgICAgICAgdmFsdWU6IDYwMAogICAgICB9XSwKICAgICAgZ2VvSnNvbkRhdGE6IG51bGwKICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgdGhpcy5pbml0Q2hhcnQoKTsKICAgIH0pOwogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsKICB9LAoKICBiZWZvcmVEZXN0cm95KCkgewogICAgaWYgKHRoaXMuY2hhcnQpIHsKICAgICAgdGhpcy5jaGFydC5kaXNwb3NlKCk7CiAgICB9CgogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsKICB9LAoKICBtZXRob2RzOiB7CiAgICBhc3luYyBpbml0Q2hhcnQoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfliJ3lp4vljJblnLDlm77nu4Tku7YuLi4nKTsKCiAgICAgIGlmICghdGhpcy4kcmVmcy5tYXBDaGFydCkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WcsOWbvuWuueWZqOacquaJvuWIsCcpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy5jaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLm1hcENoYXJ0KTsKICAgICAgY29uc29sZS5sb2coJ0VDaGFydHPlrp7kvovliJvlu7rmiJDlip8nKTsgLy8g5Yqg6L296Z2S5bKb5Zyw55CG5pWw5o2uCgogICAgICB0cnkgewogICAgICAgIHRoaXMuZ2VvSnNvbkRhdGEgPSByZXF1aXJlKCcuL3FpbmdkYW8uanNvbicpOwogICAgICAgIGNvbnNvbGUubG9nKCfpnZLlspvlnLDnkIbmlbDmja7liqDovb3miJDlip8nKTsKICAgICAgICBlY2hhcnRzLnJlZ2lzdGVyTWFwKCdxaW5nZGFvJywgdGhpcy5nZW9Kc29uRGF0YSk7CiAgICAgICAgY29uc29sZS5sb2coJ+mdkuWym+WcsOWbvuazqOWGjOaIkOWKnycpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vemdkuWym+WcsOeQhuaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLAogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7CiAgICAgICAgICAgIHJldHVybiBgPGRpdiBzdHlsZT0icGFkZGluZzogOHB4OyI+CiAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6ICMwMEQ0RkY7IG1hcmdpbi1ib3R0b206IDRweDsiPiR7cGFyYW1zLm5hbWV9PC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6ICNGRkZGRkY7Ij4ke3BhcmFtcy52YWx1ZSB8fCAwfTwvZGl2PgogICAgICAgICAgICA8L2Rpdj5gOwogICAgICAgICAgfSwKICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwgMjAsIDQwLCAwLjk1KScsCiAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMEQ0RkYnLAogICAgICAgICAgYm9yZGVyV2lkdGg6IDIsCiAgICAgICAgICBib3JkZXJSYWRpdXM6IDgsCiAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMCwgMjEyLCAyNTUsIDAuMyknLAogICAgICAgICAgc2hhZG93Qmx1cjogMTAsCiAgICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICcjRkZGRkZGJywKICAgICAgICAgICAgZm9udFNpemU6IDExLAogICAgICAgICAgICBmb250RmFtaWx5OiAnTWljcm9zb2Z0IFlhSGVpLCBBcmlhbCwgc2Fucy1zZXJpZicKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGdlbzogewogICAgICAgICAgbWFwOiAncWluZ2RhbycsCiAgICAgICAgICByb2FtOiBmYWxzZSwKICAgICAgICAgIHpvb206IDEsCiAgICAgICAgICAvLyBjZW50ZXI6IFsxMjAuMzgyNiwgMzYuMDY3MV0sCiAgICAgICAgICBhc3BlY3RTY2FsZTogMC43LAogICAgICAgICAgbGF5b3V0Q2VudGVyOiBbJzUwJScsICc1MCUnXSwKICAgICAgICAgIGxheW91dFNpemU6ICc4NSUnLAogICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgIGFyZWFDb2xvcjogewogICAgICAgICAgICAgIHR5cGU6ICdyYWRpYWwnLAogICAgICAgICAgICAgIHg6IDAuNSwKICAgICAgICAgICAgICB5OiAwLjUsCiAgICAgICAgICAgICAgcjogMC44LAogICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFt7CiAgICAgICAgICAgICAgICBvZmZzZXQ6IDAsCiAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMCwgMjU1LCAyNTUsIDAuNCknCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgb2Zmc2V0OiAwLjMsCiAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMCwgMTgwLCAyNTUsIDAuNiknCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgb2Zmc2V0OiAwLjYsCiAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMCwgMTIwLCAyMDAsIDAuNyknCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgb2Zmc2V0OiAxLAogICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDAsIDYwLCAxMjAsIDAuOSknCiAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDBGRkZGJywKICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDMsCiAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLCAyNTUsIDI1NSwgMC44KScsCiAgICAgICAgICAgIHNoYWRvd0JsdXI6IDI1LAogICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLAogICAgICAgICAgICBzaGFkb3dPZmZzZXRZOiAxNSwKICAgICAgICAgICAgb3BhY2l0eTogMC45CiAgICAgICAgICB9LAogICAgICAgICAgZW1waGFzaXM6IHsKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgYXJlYUNvbG9yOiB7CiAgICAgICAgICAgICAgICB0eXBlOiAncmFkaWFsJywKICAgICAgICAgICAgICAgIHg6IDAuNSwKICAgICAgICAgICAgICAgIHk6IDAuNSwKICAgICAgICAgICAgICAgIHI6IDAuOCwKICAgICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFt7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyknCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMC4yLAogICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMCwgMjU1LCAyNTUsIDAuNyknCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMC41LAogICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMCwgMjAwLCAyNTUsIDAuOCknCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMSwKICAgICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDAsIDEwMCwgMjAwLCAwLjkpJwogICAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI0ZGRkZGRicsCiAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDQsCiAgICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDEpJywKICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAzMCwKICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLAogICAgICAgICAgICAgIHNoYWRvd09mZnNldFk6IDIwLAogICAgICAgICAgICAgIG9wYWNpdHk6IDEKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgIGNvbG9yOiAnI0U2RjdGRicsCiAgICAgICAgICAgIGZvbnRTaXplOiAxMSwKICAgICAgICAgICAgdGV4dFNoYWRvd0NvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjgpJywKICAgICAgICAgICAgdGV4dFNoYWRvd0JsdXI6IDQsCiAgICAgICAgICAgIHRleHRTaGFkb3dPZmZzZXRYOiAxLAogICAgICAgICAgICB0ZXh0U2hhZG93T2Zmc2V0WTogMQogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgdHlwZTogJ21hcCcsCiAgICAgICAgICBtYXA6ICdxaW5nZGFvJywKICAgICAgICAgIGRhdGE6IHRoaXMubWFwRGF0YSwKICAgICAgICAgIGdlb0luZGV4OiAwLAogICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgIG9wYWNpdHk6IDAKICAgICAgICAgIH0sCiAgICAgICAgICBlbXBoYXNpczogewogICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICBvcGFjaXR5OiAwCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0sIC8vIOa3u+WKoOWPkeWFiei+ueeVjOaViOaenAogICAgICAgIHsKICAgICAgICAgIHR5cGU6ICdsaW5lcycsCiAgICAgICAgICBjb29yZGluYXRlU3lzdGVtOiAnZ2VvJywKICAgICAgICAgIGRhdGE6IFtdLAogICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgIGNvbG9yOiAnIzAwRkZGRicsCiAgICAgICAgICAgIHdpZHRoOiAyLAogICAgICAgICAgICBvcGFjaXR5OiAwLjgsCiAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgwLCAyNTUsIDI1NSwgMC44KScsCiAgICAgICAgICAgIHNoYWRvd0JsdXI6IDEwCiAgICAgICAgICB9LAogICAgICAgICAgZWZmZWN0OiB7CiAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgIHBlcmlvZDogMywKICAgICAgICAgICAgdHJhaWxMZW5ndGg6IDAuMSwKICAgICAgICAgICAgY29sb3I6ICcjRkZGRkZGJywKICAgICAgICAgICAgc3ltYm9sU2l6ZTogMwogICAgICAgICAgfQogICAgICAgIH0sIC8vIOa3u+WKoOaVo+eCueWbvuaYvuekuuaVsOaNrgogICAgICAgIHsKICAgICAgICAgIHR5cGU6ICdzY2F0dGVyJywKICAgICAgICAgIGNvb3JkaW5hdGVTeXN0ZW06ICdnZW8nLAogICAgICAgICAgZGF0YTogdGhpcy5tYXBEYXRhLm1hcChpdGVtID0+ICh7CiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwKICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiB7CiAgICAgICAgICAgICAgICB0eXBlOiAncmFkaWFsJywKICAgICAgICAgICAgICAgIHg6IDAuNSwKICAgICAgICAgICAgICAgIHk6IDAuNSwKICAgICAgICAgICAgICAgIHI6IDAuNSwKICAgICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFt7CiAgICAgICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICAgICAgY29sb3I6ICcjRkZGRkZGJwogICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICBvZmZzZXQ6IDAuNSwKICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMDBGRkZGJwogICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICBvZmZzZXQ6IDEsCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzAwODBGRicKICAgICAgICAgICAgICAgIH1dCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMCwgMjU1LCAyNTUsIDEpJywKICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxNSwKICAgICAgICAgICAgICBib3JkZXJDb2xvcjogJyNGRkZGRkYnLAogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAyCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pKSwKICAgICAgICAgIHN5bWJvbFNpemU6IGZ1bmN0aW9uICh2YWwpIHsKICAgICAgICAgICAgcmV0dXJuIE1hdGgubWF4KHZhbFsxXSAvIDgwLCAxMik7CiAgICAgICAgICB9LAogICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgZm9ybWF0dGVyOiAne2J9XG57Y30nLAogICAgICAgICAgICBwb3NpdGlvbjogJ3RvcCcsCiAgICAgICAgICAgIGNvbG9yOiAnI0U2RjdGRicsCiAgICAgICAgICAgIGZvbnRTaXplOiAxMSwKICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLAogICAgICAgICAgICB0ZXh0U2hhZG93Q29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuOSknLAogICAgICAgICAgICB0ZXh0U2hhZG93Qmx1cjogMywKICAgICAgICAgICAgdGV4dEJvcmRlckNvbG9yOiAncmdiYSgwLCAyNTUsIDI1NSwgMC41KScsCiAgICAgICAgICAgIHRleHRCb3JkZXJXaWR0aDogMQogICAgICAgICAgfSwKICAgICAgICAgIGVtcGhhc2lzOiB7CiAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgICBmb250U2l6ZTogMTMsCiAgICAgICAgICAgICAgY29sb3I6ICcjRkZGRkZGJwogICAgICAgICAgICB9LAogICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAyNSwKICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMSknCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9OwogICAgICB0aGlzLmNoYXJ0LnNldE9wdGlvbihvcHRpb24pOyAvLyDmt7vliqDngrnlh7vkuovku7YKCiAgICAgIHRoaXMuY2hhcnQub24oJ2NsaWNrJywgcGFyYW1zID0+IHsKICAgICAgICBjb25zb2xlLmxvZygn5Zyw5Zu+54K55Ye75LqL5Lu2OicsIHBhcmFtcyk7CgogICAgICAgIGlmIChwYXJhbXMuY29tcG9uZW50VHlwZSA9PT0gJ3NlcmllcycpIHsKICAgICAgICAgIHRoaXMuaGFuZGxlUmVnaW9uQ2xpY2soewogICAgICAgICAgICBuYW1lOiBwYXJhbXMubmFtZSwKICAgICAgICAgICAgdmFsdWU6IHBhcmFtcy52YWx1ZSwKICAgICAgICAgICAgdHlwZTogJ3JlZ2lvbicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIGhhbmRsZVJlZ2lvbkNsaWNrKHJlZ2lvbikgewogICAgICB0aGlzLiRlbWl0KCdyZWdpb24tY2xpY2snLCByZWdpb24pOwogICAgICBjb25zb2xlLmxvZygn54K55Ye75Zyw5Yy6OicsIHJlZ2lvbi5uYW1lLCAn5pWw5YC8OicsIHJlZ2lvbi52YWx1ZSk7CiAgICB9LAoKICAgIGhhbmRsZVJlc2l6ZSgpIHsKICAgICAgaWYgKHRoaXMuY2hhcnQpIHsKICAgICAgICB0aGlzLmNoYXJ0LnJlc2l6ZSgpOwogICAgICB9CiAgICB9CgogIH0KfTs="}, {"version": 3, "mappings": "AAaA;AACA;AAEA;EACAA,oBADA;;EAEAC;IACA;MACAC,WADA;MAEAC,UACA;QAAAH;QAAAI;MAAA,CADA,EAEA;QAAAJ;QAAAI;MAAA,CAFA,EAGA;QAAAJ;QAAAI;MAAA,CAHA,EAIA;QAAAJ;QAAAI;MAAA,CAJA,EAKA;QAAAJ;QAAAI;MAAA,CALA,EAMA;QAAAJ;QAAAI;MAAA,CANA,EAOA;QAAAJ;QAAAI;MAAA,CAPA,EAQA;QAAAJ;QAAAI;MAAA,CARA,EASA;QAAAJ;QAAAI;MAAA,CATA,EAUA;QAAAJ;QAAAI;MAAA,CAVA,CAFA;MAcAC;IAdA;EAgBA,CAnBA;;EAoBAC;IACA;MACA;IACA,CAFA;IAGAC;EACA,CAzBA;;EA0BAC;IACA;MACA;IACA;;IACAD;EACA,CA/BA;;EAgCAE;IACA;MACAC;;MACA;QACAA;QACA;MACA;;MAEA;MACAA,6BARA,CAUA;;MACA;QACA;QACAA;QACAC;QACAD;MACA,CALA,CAKA;QACAA;QACA;MACA;;MAEA;QACAE;UACAC,eADA;UAEAC;YACA;AACA;AACA;AACA,mBAHA;UAIA,CAPA;UAQAC,wCARA;UASAC,sBATA;UAUAC,cAVA;UAWAC,eAXA;UAYAC,qCAZA;UAaAC,cAbA;UAcAC;YACAC,gBADA;YAEAC,YAFA;YAGAC;UAHA;QAdA,CADA;QAqBAC;UACAC,cADA;UAEAC,WAFA;UAGAC,OAHA;UAIA;UACAC,gBALA;UAMAC,4BANA;UAOAC,iBAPA;UAQAC;YACAC;cACAC,cADA;cAEAC,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAAjB;cAAA,CADA,EAEA;gBAAAiB;gBAAAjB;cAAA,CAFA,EAGA;gBAAAiB;gBAAAjB;cAAA,CAHA,EAIA;gBAAAiB;gBAAAjB;cAAA,CAJA;YALA,CADA;YAaAN,sBAbA;YAcAC,cAdA;YAeAE,qCAfA;YAgBAC,cAhBA;YAiBAoB,gBAjBA;YAkBAC,iBAlBA;YAmBAC;UAnBA,CARA;UA6BAC;YACAX;cACAC;gBACAC,cADA;gBAEAC,MAFA;gBAGAC,MAHA;gBAIAC,MAJA;gBAKAC,aACA;kBAAAC;kBAAAjB;gBAAA,CADA,EAEA;kBAAAiB;kBAAAjB;gBAAA,CAFA,EAGA;kBAAAiB;kBAAAjB;gBAAA,CAHA,EAIA;kBAAAiB;kBAAAjB;gBAAA,CAJA;cALA,CADA;cAaAN,sBAbA;cAcAC,cAdA;cAeAE,qCAfA;cAgBAC,cAhBA;cAiBAoB,gBAjBA;cAkBAC,iBAlBA;cAmBAC;YAnBA;UADA,CA7BA;UAoDAE;YACAC,UADA;YAEAvB,gBAFA;YAGAC,YAHA;YAIAuB,qCAJA;YAKAC,iBALA;YAMAC,oBANA;YAOAC;UAPA;QApDA,CArBA;QAmFAC,SACA;UACAhB,WADA;UAEAR,cAFA;UAGAzB,kBAHA;UAIAkD,WAJA;UAKAnB;YACAU;UADA,CALA;UAQAC;YACAX;cACAU;YADA;UADA,CARA;UAaAE;YACAC;UADA;QAbA,CADA,EAkBA;QACA;UACAX,aADA;UAEAkB,uBAFA;UAGAnD,QAHA;UAIAoD;YACA/B,gBADA;YAEAgC,QAFA;YAGAZ,YAHA;YAIAvB,qCAJA;YAKAC;UALA,CAJA;UAWAmC;YACAV,UADA;YAEAW,SAFA;YAGAC,gBAHA;YAIAnC,gBAJA;YAKAoC;UALA;QAXA,CAnBA,EAsCA;QACA;UACAxB,eADA;UAEAkB,uBAFA;UAGAnD;YACAD,eADA;YAEAI,iBAFA;YAGA4B;cACAV;gBACAY,cADA;gBAEAC,MAFA;gBAGAC,MAHA;gBAIAC,MAJA;gBAKAC,aACA;kBAAAC;kBAAAjB;gBAAA,CADA,EAEA;kBAAAiB;kBAAAjB;gBAAA,CAFA,EAGA;kBAAAiB;kBAAAjB;gBAAA,CAHA;cALA,CADA;cAYAH,mCAZA;cAaAC,cAbA;cAcAJ,sBAdA;cAeAC;YAfA;UAHA,GAHA;UAwBAyC;YACA;UACA,CA1BA;UA2BAd;YACAC,UADA;YAEA/B,qBAFA;YAGA6C,eAHA;YAIArC,gBAJA;YAKAC,YALA;YAMAqC,kBANA;YAOAd,qCAPA;YAQAC,iBARA;YASAc,yCATA;YAUAC;UAVA,CA3BA;UAuCAnB;YACAC;cACAC,UADA;cAEAtB,YAFA;cAGAD;YAHA,CADA;YAMAU;cACAZ,cADA;cAEAD;YAFA;UANA;QAvCA,CAvCA;MAnFA;MA+KA,6BApMA,CAqMA;;MACA;QACAT;;QACA;UACA;YACAV,iBADA;YAEAI,mBAFA;YAGA8B;UAHA;QAKA;MACA,CATA;IAUA,CAjNA;;IAmNA6B;MACA;MACArD;IACA,CAtNA;;IAwNAsD;MACA;QACA;MACA;IACA;;EA5NA;AAhCA", "names": ["name", "data", "chart", "mapData", "value", "geoJsonData", "mounted", "window", "<PERSON><PERSON><PERSON><PERSON>", "methods", "console", "echarts", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textStyle", "color", "fontSize", "fontFamily", "geo", "map", "roam", "zoom", "aspectScale", "layoutCenter", "layoutSize", "itemStyle", "areaColor", "type", "x", "y", "r", "colorStops", "offset", "shadowOffsetX", "shadowOffsetY", "opacity", "emphasis", "label", "show", "textShadowColor", "textShadowBlur", "textShadowOffsetX", "textShadowOffsetY", "series", "geoIndex", "coordinateSystem", "lineStyle", "width", "effect", "period", "trailLength", "symbolSize", "position", "fontWeight", "textBorderColor", "textBorder<PERSON>idth", "handleRegionClick", "handleResize"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["MapComponent.vue"], "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <!-- 背景放射光线效果 -->\n    <div class=\"background-rays\">\n      <div class=\"ray\" v-for=\"i in 24\" :key=\"i\" :style=\"{ transform: `rotate(${i * 15}deg)` }\"></div>\n    </div>\n    <!-- 圆形扫描线效果 -->\n    <div class=\"scan-circle\"></div>\n    <div ref=\"mapChart\" class=\"map-chart\"></div>\n  </div>\n</template>\n\n<script>\nimport echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'MapComponent',\n  data () {\n    return {\n      chart: null,\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      geoJsonData: null\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n    window.addEventListener('resize', this.handleResize)\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    async initChart () {\n      console.log('初始化地图组件...')\n      if (!this.$refs.mapChart) {\n        console.error('地图容器未找到')\n        return\n      }\n\n      this.chart = echarts.init(this.$refs.mapChart)\n      console.log('ECharts实例创建成功')\n\n      // 加载青岛地理数据\n      try {\n        this.geoJsonData = require('./qingdao.json')\n        console.log('青岛地理数据加载成功')\n        echarts.registerMap('qingdao', this.geoJsonData)\n        console.log('青岛地图注册成功')\n      } catch (error) {\n        console.error('加载青岛地理数据失败:', error)\n        return\n      }\n\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: 'qingdao',\n          roam: false,\n          zoom: 1,\n          // center: [120.3826, 36.0671],\n          aspectScale: 0.7,\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '85%',\n          itemStyle: {\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.5,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 255, 255, 0.4)' },\n                { offset: 0.3, color: 'rgba(0, 180, 255, 0.6)' },\n                { offset: 0.6, color: 'rgba(0, 120, 200, 0.7)' },\n                { offset: 1, color: 'rgba(0, 60, 120, 0.9)' }\n              ]\n            },\n            borderColor: '#00FFFF',\n            borderWidth: 3,\n            shadowColor: 'rgba(0, 255, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 15,\n            opacity: 0.9\n          },\n          emphasis: {\n            itemStyle: {\n              areaColor: {\n                type: 'radial',\n                x: 0.5,\n                y: 0.5,\n                r: 0.8,\n                colorStops: [\n                  { offset: 0, color: 'rgba(255, 255, 255, 0.3)' },\n                  { offset: 0.2, color: 'rgba(0, 255, 255, 0.7)' },\n                  { offset: 0.5, color: 'rgba(0, 200, 255, 0.8)' },\n                  { offset: 1, color: 'rgba(0, 100, 200, 0.9)' }\n                ]\n              },\n              borderColor: '#FFFFFF',\n              borderWidth: 4,\n              shadowColor: 'rgba(255, 255, 255, 1)',\n              shadowBlur: 30,\n              shadowOffsetX: 0,\n              shadowOffsetY: 20,\n              opacity: 1\n            }\n          },\n          label: {\n            show: true,\n            color: '#E6F7FF',\n            fontSize: 11,\n            textShadowColor: 'rgba(0, 0, 0, 0.8)',\n            textShadowBlur: 4,\n            textShadowOffsetX: 1,\n            textShadowOffsetY: 1\n          }\n        },\n        series: [\n          {\n            type: 'map',\n            map: 'qingdao',\n            data: this.mapData,\n            geoIndex: 0,\n            itemStyle: {\n              opacity: 0\n            },\n            emphasis: {\n              itemStyle: {\n                opacity: 0\n              }\n            },\n            label: {\n              show: false\n            }\n          },\n          // 添加发光边界效果\n          {\n            type: 'lines',\n            coordinateSystem: 'geo',\n            data: [],\n            lineStyle: {\n              color: '#00FFFF',\n              width: 2,\n              opacity: 0.8,\n              shadowColor: 'rgba(0, 255, 255, 0.8)',\n              shadowBlur: 10\n            },\n            effect: {\n              show: true,\n              period: 3,\n              trailLength: 0.1,\n              color: '#FFFFFF',\n              symbolSize: 3\n            }\n          },\n          // 添加散点图显示数据\n          {\n            type: 'scatter',\n            coordinateSystem: 'geo',\n            data: this.mapData.map(item => ({\n              name: item.name,\n              value: item.value,\n              itemStyle: {\n                color: {\n                  type: 'radial',\n                  x: 0.5,\n                  y: 0.5,\n                  r: 0.5,\n                  colorStops: [\n                    { offset: 0, color: '#FFFFFF' },\n                    { offset: 0.5, color: '#00FFFF' },\n                    { offset: 1, color: '#0080FF' }\n                  ]\n                },\n                shadowColor: 'rgba(0, 255, 255, 1)',\n                shadowBlur: 15,\n                borderColor: '#FFFFFF',\n                borderWidth: 2\n              }\n            })),\n            symbolSize: function (val) {\n              return Math.max(val[1] / 80, 12)\n            },\n            label: {\n              show: true,\n              formatter: '{b}\\n{c}',\n              position: 'top',\n              color: '#E6F7FF',\n              fontSize: 11,\n              fontWeight: 'bold',\n              textShadowColor: 'rgba(0, 0, 0, 0.9)',\n              textShadowBlur: 3,\n              textBorderColor: 'rgba(0, 255, 255, 0.5)',\n              textBorderWidth: 1\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 13,\n                color: '#FFFFFF'\n              },\n              itemStyle: {\n                shadowBlur: 25,\n                shadowColor: 'rgba(255, 255, 255, 1)'\n              }\n            }\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        console.log('地图点击事件:', params)\n        if (params.componentType === 'series') {\n          this.handleRegionClick({\n            name: params.name,\n            value: params.value,\n            type: 'region'\n          })\n        }\n      })\n    },\n\n    handleRegionClick (region) {\n      this.$emit('region-click', region)\n      console.log('点击地区:', region.name, '数值:', region.value)\n    },\n\n    handleResize () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: radial-gradient(circle at center, rgba(0, 40, 80, 0.8) 0%, rgba(0, 20, 40, 0.95) 70%, rgba(0, 10, 20, 1) 100%);\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n/* 背景放射光线效果 */\n.background-rays {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 100%;\n  height: 100%;\n  transform: translate(-50%, -50%);\n  z-index: 1;\n  pointer-events: none;\n}\n\n.ray {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 2px;\n  height: 50%;\n  background: linear-gradient(to bottom,\n      rgba(0, 255, 255, 0.3) 0%,\n      rgba(0, 200, 255, 0.2) 30%,\n      rgba(0, 150, 255, 0.1) 60%,\n      transparent 100%);\n  transform-origin: 0 0;\n  animation: rayPulse 4s ease-in-out infinite;\n}\n\n.ray:nth-child(odd) {\n  animation-delay: 0.5s;\n}\n\n.ray:nth-child(3n) {\n  animation-delay: 1s;\n}\n\n/* 圆形扫描线效果 */\n.scan-circle {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 80%;\n  height: 80%;\n  border: 2px solid transparent;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 2;\n  pointer-events: none;\n  background: conic-gradient(from 0deg,\n      transparent 0deg,\n      rgba(0, 255, 255, 0.3) 30deg,\n      rgba(0, 255, 255, 0.6) 60deg,\n      rgba(0, 255, 255, 0.3) 90deg,\n      transparent 120deg,\n      transparent 360deg);\n  animation: scanRotate 8s linear infinite;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes rayPulse {\n\n  0%,\n  100% {\n    opacity: 0.3;\n    transform: scaleY(1);\n  }\n\n  50% {\n    opacity: 0.8;\n    transform: scaleY(1.2);\n  }\n}\n\n@keyframes scanRotate {\n  0% {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n\n  100% {\n    transform: translate(-50%, -50%) rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}