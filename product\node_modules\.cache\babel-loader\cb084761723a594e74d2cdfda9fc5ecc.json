{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755574610613}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAOA;;AACAA,kC,CAAA;;;AAEA;EACAC,oBADA;EAEAC,qCAFA;;EAGAC;IACA;MACAC;QACAC;UACAC,eADA;UAEAC;YACA;AACA;AACA;AACA,mBAHA;UAIA,CAPA;UAQAC,wCARA;UASAC,sBATA;UAUAC,cAVA;UAWAC,eAXA;UAYAC,qCAZA;UAaAC,cAbA;UAcAC;YACAC,gBADA;YAEAC,YAFA;YAGAC;UAHA;QAdA,CADA;QAqBAC;UACAC,WADA;UAEAC,4BAFA;UAGAC,iBAHA;UAIAC,WAJA;UAKAC;YACAb,cADA;YAEAD,sBAFA;YAGAG,qCAHA;YAIAC,cAJA;YAKAW,gBALA;YAMAC;UANA,CALA;UAaAC;YACAhB,cADA;YAEAD,sBAFA;YAGAG,uCAHA;YAIAC,cAJA;YAKAW,gBALA;YAMAC;UANA;QAbA,CArBA;QA2CAE;MA3CA,CADA;MA8CAC,UACA;QAAA3B;QAAA4B;MAAA,CADA,EAEA;QAAA5B;QAAA4B;MAAA,CAFA,EAGA;QAAA5B;QAAA4B;MAAA,CAHA,EAIA;QAAA5B;QAAA4B;MAAA,CAJA,EAKA;QAAA5B;QAAA4B;MAAA,CALA,EAMA;QAAA5B;QAAA4B;MAAA,CANA,EAOA;QAAA5B;QAAA4B;MAAA,CAPA,EAQA;QAAA5B;QAAA4B;MAAA,CARA,EASA;QAAA5B;QAAA4B;MAAA,CATA,EAUA;QAAA5B;QAAA4B;MAAA,CAVA,CA9CA;MA0DAC,kBA1DA;MA2DAC;IA3DA;EA6DA,CAjEA;;EAkEAC;IACA;MACA;IACA,CAFA;EAGA,CAtEA;;EAuEAC;IACAC;MACA;MACA;IACA,CAJA;;IAKA;MACA;MACA;;MACA;QACA;;QACA;UACA;;UACA;YACAC;cAAAC;YAAA;UACA;QACA;MACA;;MACA,4BAZA,CAaA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA7GA;;IA+GAC;MACA;MACA;MACAC;MACA;MACA;MACAC;MACAC,sBACA,0BADA;QAEAb;MAFA;MAIAa;MACAA;QAAA;;QACA;QACA,+HAFA,CAGA;;QACAA;UACAb,SACA,oBADA,EAEA,0EAFA;QADA;MAMA,CAVA;MAWAc;QACA;UACAD;QACA;MACA,CAJA;IAKA,CA3IA;;IA6IAE;MACA;MACA;MACA;IACA,CAjJA;;IAmJAC;MACA;QACAC,eADA;QAEAC,uBAFA;QAGA1C,eAHA;QAIA2C,UAJA;QAKAC,oBALA;QAMAC,WANA;QAOAC;MAPA;IASA,CA7JA;;IA+JAC;MACA;QACA9B,4BADA;QAEAC,iBAFA;QAGApB,WAHA;QAIA2C,WAJA;QAKAzB,WALA;QAMA6B,WANA;QAOAG,uBAPA;QAQAC;UACAN,UADA;UAEA9B,YAFA;UAGAqC,kBAHA;UAIAtC,gBAJA;UAKAuC,kBALA;UAMAC,qCANA;UAOAC,iBAPA;UAQAC,oBARA;UASAC,oBATA;UAUAnD;YACA;UACA,CAZA;UAaAmB;YACAoB,UADA;YAEA9B,YAFA;YAGAD,gBAHA;YAIAwC,yCAJA;YAKAC;UALA;QAbA,CARA;QA6BAlC,WA7BA;QA8BAC;UACAd,sBADA;UAEAC,cAFA;UAGAE,mCAHA;UAIAC,cAJA;UAKAW,gBALA;UAMAC,gBANA;UAOAkC;YACAf,cADA;YAEAgB,MAFA;YAGAC,MAHA;YAIAC,MAJA;YAKAC,aACA;cAAAC;cAAAjD;YAAA,CADA,EAEA;cAAAiD;cAAAjD;YAAA,CAFA,EAGA;cAAAiD;cAAAjD;YAAA,CAHA,EAIA;cAAAiD;cAAAjD;YAAA,CAJA;UALA;QAPA,CA9BA;QAkDAW;UACAH;YACAd,sBADA;YAEAG,qCAFA;YAGAC,cAHA;YAIAW,gBAJA;YAKAC,gBALA;YAMAf,cANA;YAOAiD;cACAf,cADA;cAEAgB,MAFA;cAGAC,MAHA;cAIAC,MAJA;cAKAC,aACA;gBAAAC;gBAAAjD;cAAA,CADA,EAEA;gBAAAiD;gBAAAjD;cAAA,CAFA,EAGA;gBAAAiD;gBAAAjD;cAAA,CAHA,EAIA;gBAAAiD;gBAAAjD;cAAA,CAJA;YALA;UAPA;QADA;MAlDA;IAyEA;;EAzOA;AAvEA", "names": ["require", "name", "props", "data", "options", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "borderRadius", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textStyle", "color", "fontSize", "fontFamily", "geo", "map", "layoutCenter", "layoutSize", "roam", "itemStyle", "shadowOffsetX", "shadowOffsetY", "emphasis", "series", "mapData", "value", "echartObjRef", "regionList", "mounted", "methods", "splitFileName", "geoJson", "features", "renderMap", "dom", "echarts", "echartObj", "window", "getOptions", "getSeriesData", "type", "coordinateSystem", "show", "symbolSize", "geoIndex", "symbolOffset", "getMapSeries", "showLegendSymbol", "label", "position", "fontWeight", "textShadowColor", "textShadowBlur", "textShadowOffsetX", "textShadowOffsetY", "areaColor", "x", "y", "r", "colorStops", "offset"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["MapComponent.vue"], "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 3,\n            borderColor: '#00D4FF',\n            shadowColor: 'rgba(0, 212, 255, 0.8)',\n            shadowBlur: 15,\n            shadowOffsetX: 0,\n            shadowOffsetY: 8\n          },\n          emphasis: {\n            borderWidth: 4,\n            borderColor: '#FFFFFF',\n            shadowColor: 'rgba(255, 255, 255, 0.9)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 12\n          }\n        },\n        series: []\n      },\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n      // const option = {\n\n      //   geo: {\n      //     map: '智慧人大',\n      //     layoutCenter: ['50%', '50%'],\n      //     layoutSize: '90%',\n      //     roam: false,\n      //     zoom: 1,\n\n      //     label: {\n      //       show: true,\n      //       color: '#E6F7FF',\n      //       fontSize: 11,\n      //       textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //       textShadowBlur: 4,\n      //       textShadowOffsetX: 1,\n      //       textShadowOffsetY: 1\n      //     }\n      //   },\n      //   series: [\n      //     {\n      //       type: 'map',\n      //       map: 'qingdao',\n      //       data: this.data,\n      //       geoIndex: 0,\n      //       itemStyle: {\n      //         opacity: 0\n      //       },\n      //       emphasis: {\n      //         itemStyle: {\n      //           opacity: 0\n      //         }\n      //       },\n      //       label: {\n      //         show: false\n      //       }\n      //     },\n      //     // 添加散点图显示数据\n      //     {\n      //       type: 'scatter',\n      //       coordinateSystem: 'geo',\n      //       data: this.data.map(item => ({\n      //         name: item.name,\n      //         value: item.value,\n      //         itemStyle: {\n      //           color: '#FFD600',\n      //           shadowColor: 'rgba(255, 214, 0, 0.6)',\n      //           shadowBlur: 8,\n      //           borderColor: '#FFFFFF',\n      //           borderWidth: 1\n      //         }\n      //       })),\n      //       symbolSize: function (val) {\n      //         return Math.max(val[1] / 100, 8)\n      //       },\n      //       label: {\n      //         show: true,\n      //         formatter: '{b}\\n{c}',\n      //         position: 'top',\n      //         color: '#FFFFFF',\n      //         fontSize: 10,\n      //         fontWeight: 'bold',\n      //         textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //         textShadowBlur: 2\n      //       },\n      //       emphasis: {\n      //         label: {\n      //           show: true,\n      //           fontSize: 12,\n      //           color: '#FFD600'\n      //         },\n      //         itemStyle: {\n      //           shadowBlur: 12,\n      //           shadowColor: 'rgba(255, 214, 0, 0.8)'\n      //         }\n      //       }\n      //     }\n      //   ]\n      // }\n      // this.chart.setOption(option)\n      // // 添加点击事件\n      // this.chart.on('click', (params) => {\n      //   console.log('地图点击事件:', params)\n      //   if (params.componentType === 'series') {\n      //     this.handleRegionClick({\n      //       name: params.name,\n      //       value: params.value,\n      //       type: 'region'\n      //     })\n      //   }\n      // })\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 16,\n          position: 'center',\n          color: '#FFFFFF',\n          fontWeight: 'bold',\n          textShadowColor: 'rgba(0, 0, 0, 0.8)',\n          textShadowBlur: 8,\n          textShadowOffsetX: 2,\n          textShadowOffsetY: 2,\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            fontSize: 18,\n            color: '#00FFFF',\n            textShadowColor: 'rgba(0, 255, 255, 0.8)',\n            textShadowBlur: 12\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#00D4FF',\n          borderWidth: 3,\n          shadowColor: 'rgba(0, 212, 255, 1)',\n          shadowBlur: 20,\n          shadowOffsetX: 0,\n          shadowOffsetY: 0,\n          areaColor: {\n            type: 'radial',\n            x: 0.3,\n            y: 0.2,\n            r: 1.2,\n            colorStops: [\n              { offset: 0, color: 'rgba(0, 255, 255, 0.4)' },\n              { offset: 0.3, color: 'rgba(0, 180, 255, 0.6)' },\n              { offset: 0.7, color: 'rgba(30, 120, 200, 0.8)' },\n              { offset: 1, color: 'rgba(10, 29, 76, 0.9)' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#FFFFFF',\n            shadowColor: 'rgba(255, 255, 255, 1)',\n            shadowBlur: 35,\n            shadowOffsetX: 0,\n            shadowOffsetY: 0,\n            borderWidth: 4,\n            areaColor: {\n              type: 'radial',\n              x: 0.3,\n              y: 0.2,\n              r: 1.2,\n              colorStops: [\n                { offset: 0, color: 'rgba(255, 255, 255, 0.3)' },\n                { offset: 0.2, color: 'rgba(0, 255, 255, 0.7)' },\n                { offset: 0.6, color: 'rgba(0, 200, 255, 0.8)' },\n                { offset: 1, color: 'rgba(0, 100, 200, 0.9)' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}