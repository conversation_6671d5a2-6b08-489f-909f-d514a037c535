<template>
  <div class="map-container">
    <div ref="mapChart" class="map-chart"></div>
  </div>
</template>

<script>
import echarts from 'echarts'
import 'echarts-gl'

export default {
  name: 'MapComponent',
  data () {
    return {
      chart: null,
      mapData: [
        { name: '市南区', value: 1200 },
        { name: '市北区', value: 1300 },
        { name: '黄岛区', value: 850 },
        { name: '崂山区', value: 700 },
        { name: '李沧区', value: 1000 },
        { name: '城阳区', value: 1100 },
        { name: '即墨区', value: 950 },
        { name: '胶州市', value: 800 },
        { name: '平度市', value: 1400 },
        { name: '莱西市', value: 600 }
      ],
      geoJsonData: null
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async initChart () {
      console.log('初始化地图组件...')
      if (!this.$refs.mapChart) {
        console.error('地图容器未找到')
        return
      }

      this.chart = echarts.init(this.$refs.mapChart)
      console.log('ECharts实例创建成功')

      // 加载青岛地理数据
      try {
        this.geoJsonData = require('./qingdao.json')
        console.log('青岛地理数据加载成功')
        echarts.registerMap('qingdao', this.geoJsonData)
        console.log('青岛地图注册成功')
      } catch (error) {
        console.error('加载青岛地理数据失败:', error)
        return
      }

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `<div style="padding: 8px;">
              <div style="color: #00D4FF; margin-bottom: 4px;">${params.name}</div>
              <div style="color: #FFFFFF;">${params.value || 0}</div>
            </div>`
          },
          backgroundColor: 'rgba(0, 20, 40, 0.95)',
          borderColor: '#00D4FF',
          borderWidth: 2,
          borderRadius: 8,
          shadowColor: 'rgba(0, 212, 255, 0.3)',
          shadowBlur: 10,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 11,
            fontFamily: 'Microsoft YaHei, Arial, sans-serif'
          }
        },
        geo: {
          map: 'qingdao',
          roam: false,
          zoom: 1,
          // center: [120.3826, 36.0671],
          aspectScale: 0.7,
          layoutCenter: ['50%', '50%'],
          layoutSize: '85%',
          itemStyle: {
            areaColor: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(0, 120, 255, 0.3)' },
                { offset: 0.5, color: 'rgba(0, 80, 200, 0.5)' },
                { offset: 1, color: 'rgba(0, 40, 120, 0.7)' }
              ]
            },
            borderColor: '#00D4FF',
            borderWidth: 2,
            shadowColor: 'rgba(0, 212, 255, 0.6)',
            shadowBlur: 15,
            shadowOffsetX: 0,
            shadowOffsetY: 8,
            opacity: 0.85
          },
          emphasis: {
            itemStyle: {
              areaColor: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(0, 180, 255, 0.6)' },
                  { offset: 0.5, color: 'rgba(0, 140, 255, 0.7)' },
                  { offset: 1, color: 'rgba(0, 100, 200, 0.8)' }
                ]
              },
              borderColor: '#00FFFF',
              borderWidth: 3,
              shadowColor: 'rgba(0, 255, 255, 0.8)',
              shadowBlur: 20,
              shadowOffsetX: 0,
              shadowOffsetY: 12,
              opacity: 0.95
            }
          },
          label: {
            show: true,
            color: '#E6F7FF',
            fontSize: 11,
            textShadowColor: 'rgba(0, 0, 0, 0.8)',
            textShadowBlur: 4,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          }
        },
        series: [
          {
            type: 'map',
            map: 'qingdao',
            data: this.mapData,
            geoIndex: 0,
            itemStyle: {
              opacity: 0
            },
            emphasis: {
              itemStyle: {
                opacity: 0
              }
            },
            label: {
              show: false
            }
          },
          // 添加散点图显示数据
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            data: this.mapData.map(item => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: '#FFD600',
                shadowColor: 'rgba(255, 214, 0, 0.8)',
                shadowBlur: 10
              }
            })),
            symbolSize: function (val) {
              return Math.max(val[1] / 100, 8)
            },
            label: {
              show: true,
              formatter: '{b}\n{c}',
              position: 'top',
              color: '#FFFFFF',
              fontSize: 10,
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.8)',
              textShadowBlur: 2
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 12,
                color: '#FFD600'
              }
            }
          }
        ]
      }
      this.chart.setOption(option)
      // 添加点击事件
      this.chart.on('click', (params) => {
        console.log('地图点击事件:', params)
        if (params.componentType === 'series') {
          this.handleRegionClick({
            name: params.name,
            value: params.value,
            type: 'region'
          })
        }
      })
    },

    handleRegionClick (region) {
      this.$emit('region-click', region)
      console.log('点击地区:', region.name, '数值:', region.value)
    },

    handleResize () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.map-chart {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 10;
}

// 动画定义
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 加载和错误状态样式 */
.loading,
.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
  color: #00D4FF;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.3);
  border-top: 3px solid #00D4FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text,
.error-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
}

.error {
  color: #ff6b7a;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 12px;
  color: #ff6b7a;
  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
