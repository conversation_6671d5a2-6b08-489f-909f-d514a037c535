<template>
  <div class="map-container">
    <!-- 背景放射光线效果 -->
    <div class="background-rays">
      <div class="ray" v-for="i in 24" :key="i" :style="{ transform: `rotate(${i * 15}deg)` }"></div>
    </div>
    <!-- 圆形扫描线效果 -->
    <div class="scan-circle"></div>
    <div ref="mapChart" class="map-chart"></div>
  </div>
</template>

<script>
import echarts from 'echarts'
import 'echarts-gl'

export default {
  name: 'MapComponent',
  data () {
    return {
      chart: null,
      mapData: [
        { name: '市南区', value: 1200 },
        { name: '市北区', value: 1300 },
        { name: '黄岛区', value: 850 },
        { name: '崂山区', value: 700 },
        { name: '李沧区', value: 1000 },
        { name: '城阳区', value: 1100 },
        { name: '即墨区', value: 950 },
        { name: '胶州市', value: 800 },
        { name: '平度市', value: 1400 },
        { name: '莱西市', value: 600 }
      ],
      geoJsonData: null
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async initChart () {
      console.log('初始化地图组件...')
      if (!this.$refs.mapChart) {
        console.error('地图容器未找到')
        return
      }

      this.chart = echarts.init(this.$refs.mapChart)
      console.log('ECharts实例创建成功')

      // 加载青岛地理数据
      try {
        this.geoJsonData = require('./qingdao.json')
        console.log('青岛地理数据加载成功')
        echarts.registerMap('qingdao', this.geoJsonData)
        console.log('青岛地图注册成功')
      } catch (error) {
        console.error('加载青岛地理数据失败:', error)
        return
      }

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `<div style="padding: 8px;">
              <div style="color: #00D4FF; margin-bottom: 4px;">${params.name}</div>
              <div style="color: #FFFFFF;">${params.value || 0}</div>
            </div>`
          },
          backgroundColor: 'rgba(0, 20, 40, 0.95)',
          borderColor: '#00D4FF',
          borderWidth: 2,
          borderRadius: 8,
          shadowColor: 'rgba(0, 212, 255, 0.3)',
          shadowBlur: 10,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 11,
            fontFamily: 'Microsoft YaHei, Arial, sans-serif'
          }
        },
        geo: {
          map: 'qingdao',
          roam: false,
          zoom: 1,
          // center: [120.3826, 36.0671],
          aspectScale: 0.7,
          layoutCenter: ['50%', '50%'],
          layoutSize: '85%',
          itemStyle: {
            areaColor: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [
                { offset: 0, color: 'rgba(0, 255, 255, 0.4)' },
                { offset: 0.3, color: 'rgba(0, 180, 255, 0.6)' },
                { offset: 0.6, color: 'rgba(0, 120, 200, 0.7)' },
                { offset: 1, color: 'rgba(0, 60, 120, 0.9)' }
              ]
            },
            borderColor: '#00FFFF',
            borderWidth: 3,
            shadowColor: 'rgba(0, 255, 255, 0.8)',
            shadowBlur: 25,
            shadowOffsetX: 0,
            shadowOffsetY: 15,
            opacity: 0.9
          },
          emphasis: {
            itemStyle: {
              areaColor: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [
                  { offset: 0, color: 'rgba(255, 255, 255, 0.3)' },
                  { offset: 0.2, color: 'rgba(0, 255, 255, 0.7)' },
                  { offset: 0.5, color: 'rgba(0, 200, 255, 0.8)' },
                  { offset: 1, color: 'rgba(0, 100, 200, 0.9)' }
                ]
              },
              borderColor: '#FFFFFF',
              borderWidth: 4,
              shadowColor: 'rgba(255, 255, 255, 1)',
              shadowBlur: 30,
              shadowOffsetX: 0,
              shadowOffsetY: 20,
              opacity: 1
            }
          },
          label: {
            show: true,
            color: '#E6F7FF',
            fontSize: 11,
            textShadowColor: 'rgba(0, 0, 0, 0.8)',
            textShadowBlur: 4,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          }
        },
        series: [
          {
            type: 'map',
            map: 'qingdao',
            data: this.mapData,
            geoIndex: 0,
            itemStyle: {
              opacity: 0
            },
            emphasis: {
              itemStyle: {
                opacity: 0
              }
            },
            label: {
              show: false
            }
          },
          // 添加发光边界效果
          {
            type: 'lines',
            coordinateSystem: 'geo',
            data: [],
            lineStyle: {
              color: '#00FFFF',
              width: 2,
              opacity: 0.8,
              shadowColor: 'rgba(0, 255, 255, 0.8)',
              shadowBlur: 10
            },
            effect: {
              show: true,
              period: 3,
              trailLength: 0.1,
              color: '#FFFFFF',
              symbolSize: 3
            }
          },
          // 添加散点图显示数据
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            data: this.mapData.map(item => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: {
                  type: 'radial',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    { offset: 0, color: '#FFFFFF' },
                    { offset: 0.5, color: '#00FFFF' },
                    { offset: 1, color: '#0080FF' }
                  ]
                },
                shadowColor: 'rgba(0, 255, 255, 1)',
                shadowBlur: 15,
                borderColor: '#FFFFFF',
                borderWidth: 2
              }
            })),
            symbolSize: function (val) {
              return Math.max(val[1] / 80, 12)
            },
            label: {
              show: true,
              formatter: '{b}\n{c}',
              position: 'top',
              color: '#E6F7FF',
              fontSize: 11,
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.9)',
              textShadowBlur: 3,
              textBorderColor: 'rgba(0, 255, 255, 0.5)',
              textBorderWidth: 1
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 13,
                color: '#FFFFFF'
              },
              itemStyle: {
                shadowBlur: 25,
                shadowColor: 'rgba(255, 255, 255, 1)'
              }
            }
          }
        ]
      }
      this.chart.setOption(option)
      // 添加点击事件
      this.chart.on('click', (params) => {
        console.log('地图点击事件:', params)
        if (params.componentType === 'series') {
          this.handleRegionClick({
            name: params.name,
            value: params.value,
            type: 'region'
          })
        }
      })
    },

    handleRegionClick (region) {
      this.$emit('region-click', region)
      console.log('点击地区:', region.name, '数值:', region.value)
    },

    handleResize () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: radial-gradient(circle at center, rgba(0, 40, 80, 0.8) 0%, rgba(0, 20, 40, 0.95) 70%, rgba(0, 10, 20, 1) 100%);
}

.map-chart {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 10;
}

/* 背景放射光线效果 */
.background-rays {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  z-index: 1;
  pointer-events: none;
}

.ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 50%;
  background: linear-gradient(to bottom,
      rgba(0, 255, 255, 0.3) 0%,
      rgba(0, 200, 255, 0.2) 30%,
      rgba(0, 150, 255, 0.1) 60%,
      transparent 100%);
  transform-origin: 0 0;
  animation: rayPulse 4s ease-in-out infinite;
}

.ray:nth-child(odd) {
  animation-delay: 0.5s;
}

.ray:nth-child(3n) {
  animation-delay: 1s;
}

/* 圆形扫描线效果 */
.scan-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  border: 2px solid transparent;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  pointer-events: none;
  background: conic-gradient(from 0deg,
      transparent 0deg,
      rgba(0, 255, 255, 0.3) 30deg,
      rgba(0, 255, 255, 0.6) 60deg,
      rgba(0, 255, 255, 0.3) 90deg,
      transparent 120deg,
      transparent 360deg);
  animation: scanRotate 8s linear infinite;
}

// 动画定义
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes rayPulse {

  0%,
  100% {
    opacity: 0.3;
    transform: scaleY(1);
  }

  50% {
    opacity: 0.8;
    transform: scaleY(1.2);
  }
}

@keyframes scanRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 加载和错误状态样式 */
.loading,
.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
  color: #00D4FF;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.3);
  border-top: 3px solid #00D4FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text,
.error-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
}

.error {
  color: #ff6b7a;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 12px;
  color: #ff6b7a;
  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
