<template>
  <div class="map-container">
    <div class="map" id="homeMap"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // 引入主题

export default {
  name: 'MapComponent',
  props: ['data', 'areaId', 'areaName'],
  data () {
    return {
      options: {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `<div style="padding: 8px;">
              <div style="color: #00D4FF; margin-bottom: 4px;">${params.name}</div>
              <div style="color: #FFFFFF;">${params.value || 0}</div>
            </div>`
          },
          backgroundColor: 'rgba(0, 20, 40, 0.95)',
          borderColor: '#00D4FF',
          borderWidth: 2,
          borderRadius: 8,
          shadowColor: 'rgba(0, 212, 255, 0.3)',
          shadowBlur: 10,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 11,
            fontFamily: 'Microsoft YaHei, Arial, sans-serif'
          }
        },
        geo: {
          map: '智慧人大',
          layoutCenter: ['50%', '50%'],
          layoutSize: '90%',
          roam: false,
          itemStyle: {
            borderWidth: 2, // 设置外层边框
            borderColor: 'rgba(0, 181, 254, 1)',
            shadowColor: 'rgba(0, 181, 254, 1)',
            shadowBlur: 0,
            shadowOffsetX: 0, // 阴影水平方向上的偏移距离。
            shadowOffsetY: 20
          },
          emphasis: {
            borderWidth: 2, // 设置外层边框
            borderColor: 'rgba(0, 181, 254, 1)',
            shadowColor: 'rgba(0, 181, 254, .1)',
            shadowBlur: 0,
            shadowOffsetX: 20, // 阴影水平方向上的偏移距离。
            shadowOffsetY: 10
          }
        },
        series: []
      },
      mapData: [
        { name: '市南区', value: 1200 },
        { name: '市北区', value: 1300 },
        { name: '黄岛区', value: 850 },
        { name: '崂山区', value: 700 },
        { name: '李沧区', value: 1000 },
        { name: '城阳区', value: 1100 },
        { name: '即墨区', value: 950 },
        { name: '胶州市', value: 800 },
        { name: '平度市', value: 1400 },
        { name: '莱西市', value: 600 }
      ],
      echartObjRef: null,
      regionList: []
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initMap()
    })
  },
  methods: {
    splitFileName (text) {
      var pattern = /\.{1}[a-z]{1,}$/
      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)
    },
    async initMap (id = this.areaId) {
      const mapJson = await import('./qingdao.json')
      let geoJson = mapJson.default
      if (id && id !== '370200' && this.data) {
        const area = this.data.find(a => a.areaId === id)
        if (area) {
          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))
          if (feature) {
            geoJson = { ...geoJson, features: [feature] }
          }
        }
      }
      this.renderMap(geoJson, id)
      // const option = {

      //   geo: {
      //     map: '智慧人大',
      //     layoutCenter: ['50%', '50%'],
      //     layoutSize: '90%',
      //     roam: false,
      //     zoom: 1,

      //     label: {
      //       show: true,
      //       color: '#E6F7FF',
      //       fontSize: 11,
      //       textShadowColor: 'rgba(0, 0, 0, 0.8)',
      //       textShadowBlur: 4,
      //       textShadowOffsetX: 1,
      //       textShadowOffsetY: 1
      //     }
      //   },
      //   series: [
      //     {
      //       type: 'map',
      //       map: 'qingdao',
      //       data: this.data,
      //       geoIndex: 0,
      //       itemStyle: {
      //         opacity: 0
      //       },
      //       emphasis: {
      //         itemStyle: {
      //           opacity: 0
      //         }
      //       },
      //       label: {
      //         show: false
      //       }
      //     },
      //     // 添加散点图显示数据
      //     {
      //       type: 'scatter',
      //       coordinateSystem: 'geo',
      //       data: this.data.map(item => ({
      //         name: item.name,
      //         value: item.value,
      //         itemStyle: {
      //           color: '#FFD600',
      //           shadowColor: 'rgba(255, 214, 0, 0.6)',
      //           shadowBlur: 8,
      //           borderColor: '#FFFFFF',
      //           borderWidth: 1
      //         }
      //       })),
      //       symbolSize: function (val) {
      //         return Math.max(val[1] / 100, 8)
      //       },
      //       label: {
      //         show: true,
      //         formatter: '{b}\n{c}',
      //         position: 'top',
      //         color: '#FFFFFF',
      //         fontSize: 10,
      //         fontWeight: 'bold',
      //         textShadowColor: 'rgba(0, 0, 0, 0.8)',
      //         textShadowBlur: 2
      //       },
      //       emphasis: {
      //         label: {
      //           show: true,
      //           fontSize: 12,
      //           color: '#FFD600'
      //         },
      //         itemStyle: {
      //           shadowBlur: 12,
      //           shadowColor: 'rgba(255, 214, 0, 0.8)'
      //         }
      //       }
      //     }
      //   ]
      // }
      // this.chart.setOption(option)
      // // 添加点击事件
      // this.chart.on('click', (params) => {
      //   console.log('地图点击事件:', params)
      //   if (params.componentType === 'series') {
      //     this.handleRegionClick({
      //       name: params.name,
      //       value: params.value,
      //       type: 'region'
      //     })
      //   }
      // })
    },

    renderMap (JSONData, areaId = this.areaId) {
      const dom = document.getElementById('homeMap')
      if (!dom) return
      dom.removeAttribute('_echarts_instance_')
      const echartObj = echarts.init(dom, 'macarons')
      this.echartObjRef = echartObj
      echarts.registerMap('智慧人大', JSONData)
      echartObj.setOption({
        ...this.getOptions(areaId),
        series: [this.getSeriesData(), this.getMapSeries(areaId)]
      })
      echartObj.off('click')
      echartObj.on('click', (param) => {
        // areaName.value = param.name
        const area = this.data?.find(a => a.name === param.name)
        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })
        echartObj.setOption({
          series: [
            this.getSeriesData(),
            this.getMapSeries(area?.areaId)
          ]
        })
      })
      window.addEventListener('resize', () => {
        if (echartObj && echartObj.resize) {
          echartObj.resize()
        }
      })
    },

    getOptions (areaId) {
      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'
      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]
      return this.options
    },

    getSeriesData () {
      return {
        type: 'scatter',
        coordinateSystem: 'geo',
        data: this.data,
        show: true,
        symbolSize: [20, 25],
        geoIndex: 0,
        symbolOffset: [0, -20]
      }
    },

    getMapSeries () {
      return {
        layoutCenter: ['50%', '50%'],
        layoutSize: '90%',
        name: 'map',
        type: 'map',
        map: '智慧人大',
        geoIndex: 2,
        showLegendSymbol: false,
        label: {
          show: true,
          fontSize: 18,
          position: 'center',
          color: '#fff',
          textShadowColor: '#00eaff',
          textShadowBlur: 10,
          formatter: function (name) {
            return name.name.length > 6 ? name.name.substring(0, 5) + '\n' + name.name.substring(5) : name.name
          },
          emphasis: {
            show: true,
            textStyle: { color: '#fff' }
          }
        },
        roam: false,
        itemStyle: {
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: 'rgba(0, 181, 254, 0.8)',
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          areaColor: {
            type: 'radial',
            x: 0.5,
            y: 0.1,
            r: 0.9,
            colorStops: [
              { offset: 0, color: '#0a1d4c' },
              { offset: 1, color: '#2176ff' }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            borderColor: '#00eaff',
            shadowColor: 'rgba(0,234,255,0.8)',
            shadowBlur: 30,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            borderWidth: 2,
            areaColor: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.9,
              colorStops: [
                { offset: 0, color: '#2176ff' },
                { offset: 1, color: '#0a1d4c' }
              ]
            }
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

#homeMap {
  width: 100%;
  height: 100%;
}

.map-chart {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 10;
}

// 动画定义
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 加载和错误状态样式 */
.loading,
.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
  color: #00D4FF;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.3);
  border-top: 3px solid #00D4FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text,
.error-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
}

.error {
  color: #ff6b7a;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 12px;
  color: #ff6b7a;
  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
