{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=template&id=76285fab&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1755573807337}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}