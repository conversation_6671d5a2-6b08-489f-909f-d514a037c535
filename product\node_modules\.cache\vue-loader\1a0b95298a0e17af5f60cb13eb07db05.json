{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=style&index=0&id=484c26db&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755569068527}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAkRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <!-- 背景放射光线效果 -->\n    <div class=\"background-rays\">\n      <div class=\"ray\" v-for=\"i in 24\" :key=\"i\" :style=\"{ transform: `rotate(${i * 15}deg)` }\"></div>\n    </div>\n    <!-- 圆形扫描线效果 -->\n    <div class=\"scan-circle\"></div>\n    <div ref=\"mapChart\" class=\"map-chart\"></div>\n  </div>\n</template>\n\n<script>\nimport echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'MapComponent',\n  data () {\n    return {\n      chart: null,\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      geoJsonData: null\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n    window.addEventListener('resize', this.handleResize)\n  },\n  beforeDestroy () {\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.handleResize)\n  },\n  methods: {\n    async initChart () {\n      console.log('初始化地图组件...')\n      if (!this.$refs.mapChart) {\n        console.error('地图容器未找到')\n        return\n      }\n\n      this.chart = echarts.init(this.$refs.mapChart)\n      console.log('ECharts实例创建成功')\n\n      // 加载青岛地理数据\n      try {\n        this.geoJsonData = require('./qingdao.json')\n        console.log('青岛地理数据加载成功')\n        echarts.registerMap('qingdao', this.geoJsonData)\n        console.log('青岛地图注册成功')\n      } catch (error) {\n        console.error('加载青岛地理数据失败:', error)\n        return\n      }\n\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: 'qingdao',\n          roam: false,\n          zoom: 1,\n          // center: [120.3826, 36.0671],\n          aspectScale: 0.7,\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '85%',\n          itemStyle: {\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.5,\n              r: 0.8,\n              colorStops: [\n                { offset: 0, color: 'rgba(0, 255, 255, 0.4)' },\n                { offset: 0.3, color: 'rgba(0, 180, 255, 0.6)' },\n                { offset: 0.6, color: 'rgba(0, 120, 200, 0.7)' },\n                { offset: 1, color: 'rgba(0, 60, 120, 0.9)' }\n              ]\n            },\n            borderColor: '#00FFFF',\n            borderWidth: 3,\n            shadowColor: 'rgba(0, 255, 255, 0.8)',\n            shadowBlur: 25,\n            shadowOffsetX: 0,\n            shadowOffsetY: 15,\n            opacity: 0.9\n          },\n          emphasis: {\n            itemStyle: {\n              areaColor: {\n                type: 'radial',\n                x: 0.5,\n                y: 0.5,\n                r: 0.8,\n                colorStops: [\n                  { offset: 0, color: 'rgba(255, 255, 255, 0.3)' },\n                  { offset: 0.2, color: 'rgba(0, 255, 255, 0.7)' },\n                  { offset: 0.5, color: 'rgba(0, 200, 255, 0.8)' },\n                  { offset: 1, color: 'rgba(0, 100, 200, 0.9)' }\n                ]\n              },\n              borderColor: '#FFFFFF',\n              borderWidth: 4,\n              shadowColor: 'rgba(255, 255, 255, 1)',\n              shadowBlur: 30,\n              shadowOffsetX: 0,\n              shadowOffsetY: 20,\n              opacity: 1\n            }\n          },\n          label: {\n            show: true,\n            color: '#E6F7FF',\n            fontSize: 11,\n            textShadowColor: 'rgba(0, 0, 0, 0.8)',\n            textShadowBlur: 4,\n            textShadowOffsetX: 1,\n            textShadowOffsetY: 1\n          }\n        },\n        series: [\n          {\n            type: 'map',\n            map: 'qingdao',\n            data: this.mapData,\n            geoIndex: 0,\n            itemStyle: {\n              opacity: 0\n            },\n            emphasis: {\n              itemStyle: {\n                opacity: 0\n              }\n            },\n            label: {\n              show: false\n            }\n          },\n          // 添加发光边界效果\n          {\n            type: 'lines',\n            coordinateSystem: 'geo',\n            data: [],\n            lineStyle: {\n              color: '#00FFFF',\n              width: 2,\n              opacity: 0.8,\n              shadowColor: 'rgba(0, 255, 255, 0.8)',\n              shadowBlur: 10\n            },\n            effect: {\n              show: true,\n              period: 3,\n              trailLength: 0.1,\n              color: '#FFFFFF',\n              symbolSize: 3\n            }\n          },\n          // 添加散点图显示数据\n          {\n            type: 'scatter',\n            coordinateSystem: 'geo',\n            data: this.mapData.map(item => ({\n              name: item.name,\n              value: item.value,\n              itemStyle: {\n                color: {\n                  type: 'radial',\n                  x: 0.5,\n                  y: 0.5,\n                  r: 0.5,\n                  colorStops: [\n                    { offset: 0, color: '#FFFFFF' },\n                    { offset: 0.5, color: '#00FFFF' },\n                    { offset: 1, color: '#0080FF' }\n                  ]\n                },\n                shadowColor: 'rgba(0, 255, 255, 1)',\n                shadowBlur: 15,\n                borderColor: '#FFFFFF',\n                borderWidth: 2\n              }\n            })),\n            symbolSize: function (val) {\n              return Math.max(val[1] / 80, 12)\n            },\n            label: {\n              show: true,\n              formatter: '{b}\\n{c}',\n              position: 'top',\n              color: '#E6F7FF',\n              fontSize: 11,\n              fontWeight: 'bold',\n              textShadowColor: 'rgba(0, 0, 0, 0.9)',\n              textShadowBlur: 3,\n              textBorderColor: 'rgba(0, 255, 255, 0.5)',\n              textBorderWidth: 1\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 13,\n                color: '#FFFFFF'\n              },\n              itemStyle: {\n                shadowBlur: 25,\n                shadowColor: 'rgba(255, 255, 255, 1)'\n              }\n            }\n          }\n        ]\n      }\n      this.chart.setOption(option)\n      // 添加点击事件\n      this.chart.on('click', (params) => {\n        console.log('地图点击事件:', params)\n        if (params.componentType === 'series') {\n          this.handleRegionClick({\n            name: params.name,\n            value: params.value,\n            type: 'region'\n          })\n        }\n      })\n    },\n\n    handleRegionClick (region) {\n      this.$emit('region-click', region)\n      console.log('点击地区:', region.name, '数值:', region.value)\n    },\n\n    handleResize () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background: radial-gradient(circle at center, rgba(0, 40, 80, 0.8) 0%, rgba(0, 20, 40, 0.95) 70%, rgba(0, 10, 20, 1) 100%);\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n/* 背景放射光线效果 */\n.background-rays {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 100%;\n  height: 100%;\n  transform: translate(-50%, -50%);\n  z-index: 1;\n  pointer-events: none;\n}\n\n.ray {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 2px;\n  height: 50%;\n  background: linear-gradient(to bottom,\n      rgba(0, 255, 255, 0.3) 0%,\n      rgba(0, 200, 255, 0.2) 30%,\n      rgba(0, 150, 255, 0.1) 60%,\n      transparent 100%);\n  transform-origin: 0 0;\n  animation: rayPulse 4s ease-in-out infinite;\n}\n\n.ray:nth-child(odd) {\n  animation-delay: 0.5s;\n}\n\n.ray:nth-child(3n) {\n  animation-delay: 1s;\n}\n\n/* 圆形扫描线效果 */\n.scan-circle {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 80%;\n  height: 80%;\n  border: 2px solid transparent;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 2;\n  pointer-events: none;\n  background: conic-gradient(from 0deg,\n      transparent 0deg,\n      rgba(0, 255, 255, 0.3) 30deg,\n      rgba(0, 255, 255, 0.6) 60deg,\n      rgba(0, 255, 255, 0.3) 90deg,\n      transparent 120deg,\n      transparent 360deg);\n  animation: scanRotate 8s linear infinite;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes rayPulse {\n\n  0%,\n  100% {\n    opacity: 0.3;\n    transform: scaleY(1);\n  }\n\n  50% {\n    opacity: 0.8;\n    transform: scaleY(1.2);\n  }\n}\n\n@keyframes scanRotate {\n  0% {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n\n  100% {\n    transform: translate(-50%, -50%) rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}