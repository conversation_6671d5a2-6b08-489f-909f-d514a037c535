{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue?vue&type=style&index=0&id=484c26db&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\MapComponent.vue", "mtime": 1755574428620}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5tYXAtY29udGFpbmVyIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgojaG9tZU1hcCB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9CgoubWFwLWNoYXJ0IHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIHotaW5kZXg6IDEwOwp9CgovLyDliqjnlLvlrprkuYkKQGtleWZyYW1lcyByb3RhdGUgewogIGZyb20gewogICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7CiAgfQoKICB0byB7CiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOwogIH0KfQoKLyog5Yqg6L295ZKM6ZSZ6K+v54q25oCB5qC35byPICovCi5sb2FkaW5nLAouZXJyb3IgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDUwJTsKICBsZWZ0OiA1MCU7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgei1pbmRleDogMTAwOwogIGNvbG9yOiAjMDBENEZGOwogIGZvbnQtZmFtaWx5OiAnTWljcm9zb2Z0IFlhSGVpJywgQXJpYWwsIHNhbnMtc2VyaWY7CiAgdGV4dC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjUpOwp9CgoubG9hZGluZy1zcGlubmVyIHsKICB3aWR0aDogNDBweDsKICBoZWlnaHQ6IDQwcHg7CiAgYm9yZGVyOiAzcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsKICBib3JkZXItdG9wOiAzcHggc29saWQgIzAwRDRGRjsKICBib3JkZXItcmFkaXVzOiA1MCU7CiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTsKICBtYXJnaW4tYm90dG9tOiAxNnB4Owp9CgoubG9hZGluZy10ZXh0LAouZXJyb3ItdGV4dCB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgbGV0dGVyLXNwYWNpbmc6IDFweDsKfQoKLmVycm9yIHsKICBjb2xvcjogI2ZmNmI3YTsKfQoKLmVycm9yLWljb24gewogIGZvbnQtc2l6ZTogMzJweDsKICBtYXJnaW4tYm90dG9tOiAxMnB4OwogIGNvbG9yOiAjZmY2YjdhOwogIHRleHQtc2hhZG93OiAwIDJweCA4cHggcmdiYSgyNTUsIDEwNywgMTIyLCAwLjUpOwp9CgpAa2V5ZnJhbWVzIHNwaW4gewogIDAlIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOwogIH0KCiAgMTAwJSB7CiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOwogIH0KfQo="}, {"version": 3, "sources": ["MapComponent.vue"], "names": [], "mappings": ";AAsTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "MapComponent.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"map-container\">\n    <div class=\"map\" id=\"homeMap\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // 引入主题\n\nexport default {\n  name: 'MapComponent',\n  props: ['data', 'areaId', 'areaName'],\n  data () {\n    return {\n      options: {\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            return `<div style=\"padding: 8px;\">\n              <div style=\"color: #00D4FF; margin-bottom: 4px;\">${params.name}</div>\n              <div style=\"color: #FFFFFF;\">${params.value || 0}</div>\n            </div>`\n          },\n          backgroundColor: 'rgba(0, 20, 40, 0.95)',\n          borderColor: '#00D4FF',\n          borderWidth: 2,\n          borderRadius: 8,\n          shadowColor: 'rgba(0, 212, 255, 0.3)',\n          shadowBlur: 10,\n          textStyle: {\n            color: '#FFFFFF',\n            fontSize: 11,\n            fontFamily: 'Microsoft YaHei, Arial, sans-serif'\n          }\n        },\n        geo: {\n          map: '智慧人大',\n          layoutCenter: ['50%', '50%'],\n          layoutSize: '90%',\n          roam: false,\n          itemStyle: {\n            borderWidth: 2, // 设置外层边框\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, 1)',\n            shadowBlur: 0,\n            shadowOffsetX: 0, // 阴影水平方向上的偏移距离。\n            shadowOffsetY: 20\n          },\n          emphasis: {\n            borderWidth: 2, // 设置外层边框\n            borderColor: 'rgba(0, 181, 254, 1)',\n            shadowColor: 'rgba(0, 181, 254, .1)',\n            shadowBlur: 0,\n            shadowOffsetX: 20, // 阴影水平方向上的偏移距离。\n            shadowOffsetY: 10\n          }\n        },\n        series: []\n      },\n      mapData: [\n        { name: '市南区', value: 1200 },\n        { name: '市北区', value: 1300 },\n        { name: '黄岛区', value: 850 },\n        { name: '崂山区', value: 700 },\n        { name: '李沧区', value: 1000 },\n        { name: '城阳区', value: 1100 },\n        { name: '即墨区', value: 950 },\n        { name: '胶州市', value: 800 },\n        { name: '平度市', value: 1400 },\n        { name: '莱西市', value: 600 }\n      ],\n      echartObjRef: null,\n      regionList: []\n    }\n  },\n  mounted () {\n    this.$nextTick(() => {\n      this.initMap()\n    })\n  },\n  methods: {\n    splitFileName (text) {\n      var pattern = /\\.{1}[a-z]{1,}$/\n      return text.slice(text.lastIndexOf('/') + 1, pattern.exec(text).index)\n    },\n    async initMap (id = this.areaId) {\n      const mapJson = await import('./qingdao.json')\n      let geoJson = mapJson.default\n      if (id && id !== '370200' && this.data) {\n        const area = this.data.find(a => a.areaId === id)\n        if (area) {\n          const feature = geoJson.features.find(f => f.properties && (f.properties.adcode === area.adcode))\n          if (feature) {\n            geoJson = { ...geoJson, features: [feature] }\n          }\n        }\n      }\n      this.renderMap(geoJson, id)\n      // const option = {\n\n      //   geo: {\n      //     map: '智慧人大',\n      //     layoutCenter: ['50%', '50%'],\n      //     layoutSize: '90%',\n      //     roam: false,\n      //     zoom: 1,\n\n      //     label: {\n      //       show: true,\n      //       color: '#E6F7FF',\n      //       fontSize: 11,\n      //       textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //       textShadowBlur: 4,\n      //       textShadowOffsetX: 1,\n      //       textShadowOffsetY: 1\n      //     }\n      //   },\n      //   series: [\n      //     {\n      //       type: 'map',\n      //       map: 'qingdao',\n      //       data: this.data,\n      //       geoIndex: 0,\n      //       itemStyle: {\n      //         opacity: 0\n      //       },\n      //       emphasis: {\n      //         itemStyle: {\n      //           opacity: 0\n      //         }\n      //       },\n      //       label: {\n      //         show: false\n      //       }\n      //     },\n      //     // 添加散点图显示数据\n      //     {\n      //       type: 'scatter',\n      //       coordinateSystem: 'geo',\n      //       data: this.data.map(item => ({\n      //         name: item.name,\n      //         value: item.value,\n      //         itemStyle: {\n      //           color: '#FFD600',\n      //           shadowColor: 'rgba(255, 214, 0, 0.6)',\n      //           shadowBlur: 8,\n      //           borderColor: '#FFFFFF',\n      //           borderWidth: 1\n      //         }\n      //       })),\n      //       symbolSize: function (val) {\n      //         return Math.max(val[1] / 100, 8)\n      //       },\n      //       label: {\n      //         show: true,\n      //         formatter: '{b}\\n{c}',\n      //         position: 'top',\n      //         color: '#FFFFFF',\n      //         fontSize: 10,\n      //         fontWeight: 'bold',\n      //         textShadowColor: 'rgba(0, 0, 0, 0.8)',\n      //         textShadowBlur: 2\n      //       },\n      //       emphasis: {\n      //         label: {\n      //           show: true,\n      //           fontSize: 12,\n      //           color: '#FFD600'\n      //         },\n      //         itemStyle: {\n      //           shadowBlur: 12,\n      //           shadowColor: 'rgba(255, 214, 0, 0.8)'\n      //         }\n      //       }\n      //     }\n      //   ]\n      // }\n      // this.chart.setOption(option)\n      // // 添加点击事件\n      // this.chart.on('click', (params) => {\n      //   console.log('地图点击事件:', params)\n      //   if (params.componentType === 'series') {\n      //     this.handleRegionClick({\n      //       name: params.name,\n      //       value: params.value,\n      //       type: 'region'\n      //     })\n      //   }\n      // })\n    },\n\n    renderMap (JSONData, areaId = this.areaId) {\n      const dom = document.getElementById('homeMap')\n      if (!dom) return\n      dom.removeAttribute('_echarts_instance_')\n      const echartObj = echarts.init(dom, 'macarons')\n      this.echartObjRef = echartObj\n      echarts.registerMap('智慧人大', JSONData)\n      echartObj.setOption({\n        ...this.getOptions(areaId),\n        series: [this.getSeriesData(), this.getMapSeries(areaId)]\n      })\n      echartObj.off('click')\n      echartObj.on('click', (param) => {\n        // areaName.value = param.name\n        const area = this.data?.find(a => a.name === param.name)\n        // emit('mapClick', { name: param.name, areaId: area?.areaId, adcode: area?.adcode })\n        echartObj.setOption({\n          series: [\n            this.getSeriesData(),\n            this.getMapSeries(area?.areaId)\n          ]\n        })\n      })\n      window.addEventListener('resize', () => {\n        if (echartObj && echartObj.resize) {\n          echartObj.resize()\n        }\n      })\n    },\n\n    getOptions (areaId) {\n      this.options.geo.layoutSize = areaId === '370200' ? '90%' : '70%'\n      this.options.series = [this.getSeriesData(), this.getMapSeries(areaId)]\n      return this.options\n    },\n\n    getSeriesData () {\n      return {\n        type: 'scatter',\n        coordinateSystem: 'geo',\n        data: this.data,\n        show: true,\n        symbolSize: [20, 25],\n        geoIndex: 0,\n        symbolOffset: [0, -20]\n      }\n    },\n\n    getMapSeries () {\n      return {\n        layoutCenter: ['50%', '50%'],\n        layoutSize: '90%',\n        name: 'map',\n        type: 'map',\n        map: '智慧人大',\n        geoIndex: 2,\n        showLegendSymbol: false,\n        label: {\n          show: true,\n          fontSize: 18,\n          position: 'center',\n          color: '#fff',\n          textShadowColor: '#00eaff',\n          textShadowBlur: 10,\n          formatter: function (name) {\n            return name.name.length > 6 ? name.name.substring(0, 5) + '\\n' + name.name.substring(5) : name.name\n          },\n          emphasis: {\n            show: true,\n            textStyle: { color: '#fff' }\n          }\n        },\n        roam: false,\n        itemStyle: {\n          borderColor: '#ffffff',\n          borderWidth: 2,\n          shadowColor: 'rgba(0, 181, 254, 0.8)',\n          shadowBlur: 10,\n          shadowOffsetX: 0,\n          shadowOffsetY: 0,\n          areaColor: {\n            type: 'radial',\n            x: 0.5,\n            y: 0.1,\n            r: 0.9,\n            colorStops: [\n              { offset: 0, color: '#0a1d4c' },\n              { offset: 1, color: '#2176ff' }\n            ]\n          }\n        },\n        emphasis: {\n          itemStyle: {\n            borderColor: '#00eaff',\n            shadowColor: 'rgba(0,234,255,0.8)',\n            shadowBlur: 30,\n            shadowOffsetX: 0,\n            shadowOffsetY: 0,\n            borderWidth: 2,\n            areaColor: {\n              type: 'radial',\n              x: 0.5,\n              y: 0.5,\n              r: 0.9,\n              colorStops: [\n                { offset: 0, color: '#2176ff' },\n                { offset: 1, color: '#0a1d4c' }\n              ]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.map-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n#homeMap {\n  width: 100%;\n  height: 100%;\n}\n\n.map-chart {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 10;\n}\n\n// 动画定义\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 加载和错误状态样式 */\n.loading,\n.error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  color: #00D4FF;\n  font-family: 'Microsoft YaHei', Arial, sans-serif;\n  text-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 212, 255, 0.3);\n  border-top: 3px solid #00D4FF;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n.loading-text,\n.error-text {\n  font-size: 16px;\n  font-weight: 500;\n  letter-spacing: 1px;\n}\n\n.error {\n  color: #ff6b7a;\n}\n\n.error-icon {\n  font-size: 32px;\n  margin-bottom: 12px;\n  color: #ff6b7a;\n  text-shadow: 0 2px 8px rgba(255, 107, 122, 0.5);\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n</style>\n"]}]}