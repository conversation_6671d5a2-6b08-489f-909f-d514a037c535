{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\api\\http.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\api\\http.js", "mtime": 1755568413953}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"version": 3, "names": ["<PERSON><PERSON>", "axios", "Qs", "router", "loginUc", "baseURL", "yunpan", "yiya<PERSON><PERSON>y", "dataCenter", "process", "env", "STAGE", "window", "location", "protocol", "host", "defaults", "timeout", "headers", "post", "interceptors", "request", "use", "config", "test", "url", "switchpage", "JSON", "parse", "sessionStorage", "getItem", "token", "prototype", "$logo", "scanning<PERSON>en", "tokenid", "bigDataUrl", "theme", "bigDataUrlJson", "indexOf", "clientTypeId", "urlReg", "exec", "method", "Object", "toString", "call", "data", "stringify", "filter", "params", "isOutSideNet", "origin", "areaId", "ActiveXObject", "loginToken", "loginAreaId", "encodeURI", "qdzxtoken", "append", "console", "log", "Authorization", "error", "Promise", "reject", "response", "code", "<PERSON><PERSON><PERSON>", "message", "errmsg", "resolve", "clear", "push", "name", "undefined", "type", "includes", "param", "key", "get", "then", "res", "catch", "err", "postform", "postformTime", "timeOut", "postformProgress", "callback", "id", "onUploadProgress", "e", "filedownload", "responseType", "fileRequest", "text", "content", "blob", "Blob", "fileName", "document", "createElement", "elink", "download", "style", "display", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob", "exportFile", "myForm", "baseURLForm", "action", "setAttribute", "submit", "_post", "postText", "toFormData", "FormData", "i", "postForm", "_exportFile", "success", "upload", "export"], "sources": ["D:/zy/xm/pc/qdzx/product/src/api/http.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport axios from 'axios'\r\nimport Qs from 'qs'\r\nimport router from '../router'\r\nimport {\r\n  Message\r\n} from 'element-ui'\r\nvar loginUc = 'http://test.dc.cszysoft.com:21429/server'\r\nvar baseURL = 'http://test.dc.cszysoft.com:20701/lzt'\r\nvar yunpan = 'http://212.64.102.79/chanpinstore'\r\nvar yiyangjy = 'http://118.25.54.81/YYSRDceshi'\r\nvar dataCenter = 'http://220.170.144.85:8081/yyrddc'\r\nif (process.env.STAGE == 'qdzx') { // eslint-disable-line\r\n  baseURL = 'http://test.dc.cszysoft.com:21408/lzt' // 青岛政协测试环境\r\n  // baseURL = 'https://qdzhzx.qingdao.gov.cn/lzt' // 青岛政协正式环境\r\n  // loginUc = 'https://qdzhzx.qingdao.gov.cn/server'\r\n  // baseURL = 'http://**************/lzt' // 青岛政协正式环境\r\n  // loginUc = 'http://**************/server'\r\n} else if (process.env.STAGE === 'zht') {\r\n  baseURL = 'https://www.hnzhihuitong.com/zht_qingdaord/a' // 智会通测试环境\r\n} else if (process.env.STAGE === 'prod') {\r\n  loginUc = `${window.location.protocol}//${window.location.host}/server`\r\n  baseURL = `${window.location.protocol}//${window.location.host}/lzt`\r\n}\r\nexport {\r\n  loginUc,\r\n  baseURL,\r\n  yunpan,\r\n  yiyangjy,\r\n  dataCenter\r\n}\r\naxios.defaults.baseURL = baseURL\r\n// 请求超时时间\r\naxios.defaults.timeout = 180000\r\n// 设置post请求头\r\naxios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'\r\n// 请求拦截器\r\naxios.interceptors.request.use(\r\n  config => {\r\n    if (/^\\/apis/.test(config.url)) {\r\n      config.baseURL = ''\r\n      return config\r\n    }\r\n    var switchpage = JSON.parse(sessionStorage.getItem('switchpage')) || ''\r\n    if (switchpage) {\r\n      config.baseURL = switchpage\r\n    }\r\n    // 自定义请求头参数\r\n    var token = ''\r\n    if (sessionStorage.getItem('qdzxtoken')) {\r\n      token = ''\r\n    } else {\r\n      token = JSON.parse(sessionStorage.getItem('token' + Vue.prototype.$logo())) || ''\r\n    }\r\n    const scanningtoken = JSON.parse(sessionStorage.getItem('scanningtoken')) || ''\r\n    let tokenid = 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n    if (token) {\r\n      tokenid = token\r\n    } else if (scanningtoken) {\r\n      tokenid = 'bearer ' + scanningtoken\r\n    }\r\n    // config.headers.isOutSideNet = window.location.origin === 'http://test.dc.cszysoft.com:21408' ? 'test' : window.location.origin === 'http://qdzhzx.qingdao.gov.cn' ? true : window.location.origin === 'http://172.20.236.51:809' ? 2 : 'test'\r\n    const bigDataUrl = sessionStorage[`BigDataUrl${sessionStorage.theme}`] || ''\r\n    if (bigDataUrl) {\r\n      const bigDataUrlJson = JSON.parse(bigDataUrl)\r\n      if (config.url.indexOf(bigDataUrlJson) !== -1) {\r\n        config.headers.clientTypeId = JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo())) || ''\r\n      }\r\n    }\r\n\r\n    var urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?/\r\n    var url = urlReg.exec(config.url)\r\n    if (url) {\r\n      if (url[0] === '103.239.154.90') {\r\n        if (config.method === 'post') {\r\n          if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n            if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n              config.data = Qs.stringify(filter(config.data))\r\n            }\r\n          }\r\n        } else if (config.method === 'get') {\r\n          config.params = filter(config.params)\r\n        }\r\n        return config\r\n      }\r\n    }\r\n    // if (url && url[0] !== urlReg.exec(loginUc)[0]) {\r\n    //   const bigDataUrl = urlReg.exec(JSON.parse(sessionStorage[`BigDataUrl${sessionStorage.theme}`]))[0]\r\n    //   if (url[0] === bigDataUrl) {\r\n    //     config.headers.clientTypeId = JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo())) || ''\r\n    //   }\r\n    //   if (url[0] === 'proposal.yiyang.gov.cn' || url[0] === '212.64.61.94') {\r\n    //     var Authorization = sessionStorage.getItem('Authorization') || ''\r\n    //     if (Authorization) {\r\n    //       Authorization = 'Bearer ' + JSON.parse(Authorization)\r\n    //       config.headers.Authorization = Authorization\r\n    //     }\r\n    //     if (config.method === 'post') {\r\n    //       if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n    //         if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n    //           config.data = Qs.stringify(filter(config.data))\r\n    //         }\r\n    //       }\r\n    //     } else if (config.method === 'get') {\r\n    //       config.params = filter(config.params)\r\n    //     }\r\n    //     return config\r\n    //   } if (url[0] === '118.25.54.81' || url[0] === '103.239.154.90') {\r\n    //     if (config.method === 'post') {\r\n    //       if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n    //         if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n    //           config.data = Qs.stringify(filter(config.data))\r\n    //         }\r\n    //       }\r\n    //     } else if (config.method === 'get') {\r\n    //       config.params = filter(config.params)\r\n    //     }\r\n    //     return config\r\n    //   }\r\n    // }\r\n    config.headers.isOutSideNet = window.location.origin === 'http://qdzhzx.qingdao.gov.cn' ? true : window.location.origin === 'http://**************' ? true : window.location.origin === 'http://*************' ? 'sdt' : 'test'\r\n    // config.headers.isOutSideNet = (`${window.location.protocol}//${window.location.host}` === 'http://qdzhzx.qingdao.gov.cn' || `${window.location.protocol}//${window.location.host}` === 'http://**************')\r\n    const areaId = JSON.parse(sessionStorage.getItem('areaId' + Vue.prototype.$logo())) || '370200'\r\n    if (!!window.ActiveXObject || 'ActiveXObject' in window) { // 判断是不是ie浏览器\r\n      if (config.method === 'post') {\r\n        // 判断是不是formdata格式\r\n        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n          config.data = Qs.stringify(filter({\r\n            loginToken: tokenid,\r\n            loginAreaId: areaId,\r\n            ...config.data\r\n          }))\r\n          if (sessionStorage.getItem('qdzxtoken')) {\r\n            if (config.url.indexOf('?') === -1) {\r\n              config.url += `?qdzxtoken=${encodeURI(JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken)}`\r\n            } else {\r\n              config.url += `&qdzxtoken=${encodeURI(JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken)}`\r\n            }\r\n          }\r\n        } else {\r\n          config.data.append('loginToken', tokenid)\r\n          config.data.append('loginAreaId', areaId)\r\n        }\r\n      } else if (config.method === 'get') {\r\n        config.params = filter({\r\n          ...config.params,\r\n          loginToken: tokenid,\r\n          loginAreaId: areaId\r\n        })\r\n        if (!config.params) {\r\n          config.params = Qs.stringify(filter({\r\n            loginToken: tokenid,\r\n            loginAreaId: areaId\r\n          }))\r\n        }\r\n      }\r\n      console.log(config)\r\n    } else {\r\n      config.headers.Authorization = tokenid\r\n      config.headers['u-login-areaId'] = areaId\r\n      if (sessionStorage.getItem('qdzxtoken')) {\r\n        config.headers.qdzxtoken = JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken\r\n      }\r\n      if (config.method === 'post') {\r\n        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n          if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n            config.data = Qs.stringify(filter(config.data))\r\n          }\r\n        }\r\n      } else if (config.method === 'get') {\r\n        config.params = filter(config.params)\r\n      }\r\n    }\r\n    return config\r\n  }, error => {\r\n    // 对请求错误做些什么\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n// 响应拦截器\r\naxios.interceptors.response.use(\r\n  response => {\r\n    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据\r\n    // 否则的话抛出错误\r\n    var code = response.data.errcode || response.data.code\r\n    var message = response.data.errmsg || response.data.message\r\n    if (code === 200) {\r\n      return Promise.resolve(response)\r\n    } else if (code === 302) {\r\n      Message.error(message)\r\n      sessionStorage.clear()\r\n      router.push({\r\n        name: 'login'\r\n      })\r\n      return Promise.reject(response)\r\n    } else if (code === 400) {\r\n      // Message.error('参数异常')\r\n      Message.error(message)\r\n      return Promise.reject(response)\r\n    } else if (code === undefined) { // undefind 为文件下载接口\r\n      return Promise.resolve(response)\r\n    } else {\r\n      Message({\r\n        message: message || '请求异常',\r\n        type: 'error'\r\n      })\r\n      return Promise.reject(response)\r\n    }\r\n  }, error => {\r\n    if (error && error.message.includes('timeout')) {\r\n      Message.error('请求超时，请稍后重试！')\r\n      return Promise.reject(error)\r\n    }\r\n    console.log('error===>>', error)\r\n    // if (error && error.response.data.errmsg) {\r\n    //   Message.error(error.response.data.errmsg)\r\n    //   return Promise.reject(error)\r\n    // }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nfunction filter (param) { // 参数过滤\r\n  var data = param\r\n  for (var key in data) {\r\n    if (data[key] === null) {\r\n      delete data[key]\r\n    }\r\n  }\r\n  return data\r\n}\r\n// 封装get请求\r\nexport const get = (url, params, headers) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.get(url, {\r\n      headers: headers,\r\n      params: params\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\n// 封装post请求加了qs序列化数据\r\nexport const post = (url, params, headers) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, filter(params), {\r\n      headers: headers\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 封装post请求用于FormData请求\r\nexport const postform = (url, params, headers) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, {\r\n      headers: headers\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 封装post请求用于FormData请求\r\nexport const postformTime = (url, params, timeOut) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, timeOut)\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 封装post请求用于FormData请求\r\nexport const postformProgress = (url, params, timeout, callback, id) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios({\r\n      url,\r\n      method: 'post',\r\n      data: params,\r\n      timeout,\r\n      onUploadProgress: e => {\r\n        callback(e, id)\r\n      }\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n/* 接收后台文件流 */\r\nexport const filedownload = (url, params, type = 'blob') => {\r\n  return new Promise((resolve, reject) => {\r\n    axios({\r\n      method: 'post',\r\n      url: url,\r\n      data: params,\r\n      responseType: type\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 接收后台文件流\r\nexport const fileRequest = (url, params, text) => {\r\n  return axios({\r\n    method: 'post',\r\n    url: url,\r\n    data: params,\r\n    responseType: 'blob'\r\n  }).then(res => {\r\n    const content = res.data\r\n    const blob = new Blob([content])\r\n    const fileName = text\r\n    if ('download' in document.createElement('a')) { // 非IE下载\r\n      const elink = document.createElement('a')\r\n      elink.download = fileName\r\n      elink.style.display = 'none'\r\n      elink.href = URL.createObjectURL(blob)\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n      document.body.removeChild(elink)\r\n    } else { // IE10+下载\r\n      navigator.msSaveBlob(blob, fileName)\r\n    }\r\n  })\r\n}\r\n// 导出文件的方法\r\nexport const exportFile = (url, params) => {\r\n  var myForm = document.createElement('form')\r\n  myForm.method = 'post'\r\n  const switchpage = JSON.parse(sessionStorage.getItem('switchpage')) || ''\r\n  var baseURLForm = baseURL\r\n  if (switchpage) {\r\n    baseURLForm = switchpage\r\n  }\r\n  myForm.action = `${baseURLForm}${url}`\r\n  document.body.appendChild(myForm)\r\n  const token = sessionStorage.getItem('token' + Vue.prototype.$logo()) || ''\r\n  let tokenid = 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n  if (token) {\r\n    tokenid = JSON.parse(token)\r\n  }\r\n  const areaId = JSON.parse(sessionStorage.getItem('areaId' + Vue.prototype.$logo())) || ''\r\n  var loginToken = document.createElement('input')\r\n  loginToken.setAttribute('name', 'loginToken')\r\n  loginToken.setAttribute('value', tokenid)\r\n  myForm.appendChild(loginToken)\r\n  var loginAreaId = document.createElement('input')\r\n  loginAreaId.setAttribute('name', 'loginAreaId')\r\n  loginAreaId.setAttribute('value', areaId)\r\n  myForm.appendChild(loginAreaId)\r\n  for (const key in params) {\r\n    var name = 'input' + key\r\n    window[name] = document.createElement('input')\r\n    window[name].setAttribute('name', key)\r\n    window[name].setAttribute('value', params[key])\r\n    myForm.appendChild(window[name])\r\n  }\r\n  myForm.submit()\r\n  document.body.removeChild(myForm)\r\n}\r\n// json 参数形式\r\nexport const _post = (url, params) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, {\r\n      headers: {\r\n        'Content-Type': 'application/json;charset=UTF-8'\r\n      }\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\nconst postText = (url, params) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, Qs.stringify(params), {\r\n      headers: {\r\n        'Content-Type': 'application/json;charset=UTF-8'\r\n      }\r\n    }).then(res => {\r\n      resolve(res)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\n\r\nfunction toFormData (params) {\r\n  const data = new FormData()\r\n  for (var i in params) {\r\n    data.append(i, params[i])\r\n  }\r\n  return data\r\n}\r\nconst postForm = (url, params, headers) => {\r\n  params = toFormData(params)\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, {\r\n      headers: headers\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\nconst _exportFile = (url, params, fileName) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios({\r\n      method: 'post',\r\n      url: url,\r\n      data: params,\r\n      responseType: 'blob'\r\n    }).then(res => {\r\n      const blob = new Blob([res.request.response])\r\n      const elink = document.createElement('a')\r\n      elink.download = fileName\r\n      elink.style.display = 'none'\r\n      elink.href = URL.createObjectURL(blob)\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      document.body.removeChild(elink)\r\n      Message.success('导出成功')\r\n      resolve(res)\r\n    }).catch(err => {\r\n      Message.error('导出失败')\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\nexport default {\r\n  get,\r\n  post: _post,\r\n  postText,\r\n  postForm,\r\n  upload: postform,\r\n  export: _exportFile\r\n}\r\n"], "mappings": ";;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,KAAP,MAAkB,OAAlB;AACA,OAAOC,EAAP,MAAe,IAAf;AACA,OAAOC,MAAP,MAAmB,WAAnB;AAIA,IAAIC,OAAO,GAAG,0CAAd;AACA,IAAIC,OAAO,GAAG,uCAAd;AACA,IAAIC,MAAM,GAAG,mCAAb;AACA,IAAIC,QAAQ,GAAG,gCAAf;AACA,IAAIC,UAAU,GAAG,mCAAjB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,KAAZ,IAAqB,MAAzB,EAAiC;EAAE;EACjCN,OAAO,GAAG,uCAAV,CAD+B,CACmB;EAClD;EACA;EACA;EACA;AACD,CAND,MAMO,IAAII,OAAO,CAACC,GAAR,CAAYC,KAAZ,KAAsB,KAA1B,EAAiC;EACtCN,OAAO,GAAG,8CAAV,CADsC,CACmB;AAC1D,CAFM,MAEA,IAAII,OAAO,CAACC,GAAR,CAAYC,KAAZ,KAAsB,MAA1B,EAAkC;EACvCP,OAAO,GAAI,GAAEQ,MAAM,CAACC,QAAP,CAAgBC,QAAS,KAAIF,MAAM,CAACC,QAAP,CAAgBE,IAAK,SAA/D;EACAV,OAAO,GAAI,GAAEO,MAAM,CAACC,QAAP,CAAgBC,QAAS,KAAIF,MAAM,CAACC,QAAP,CAAgBE,IAAK,MAA/D;AACD;;AACD,SACEX,OADF,EAEEC,OAFF,EAGEC,MAHF,EAIEC,QAJF,EAKEC,UALF;AAOAP,KAAK,CAACe,QAAN,CAAeX,OAAf,GAAyBA,OAAzB,C,CACA;;AACAJ,KAAK,CAACe,QAAN,CAAeC,OAAf,GAAyB,MAAzB,C,CACA;;AACAhB,KAAK,CAACe,QAAN,CAAeE,OAAf,CAAuBC,IAAvB,CAA4B,cAA5B,IAA8C,iDAA9C,C,CACA;;AACAlB,KAAK,CAACmB,YAAN,CAAmBC,OAAnB,CAA2BC,GAA3B,CACEC,MAAM,IAAI;EACR,IAAI,UAAUC,IAAV,CAAeD,MAAM,CAACE,GAAtB,CAAJ,EAAgC;IAC9BF,MAAM,CAAClB,OAAP,GAAiB,EAAjB;IACA,OAAOkB,MAAP;EACD;;EACD,IAAIG,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,YAAvB,CAAX,KAAoD,EAArE;;EACA,IAAIJ,UAAJ,EAAgB;IACdH,MAAM,CAAClB,OAAP,GAAiBqB,UAAjB;EACD,CARO,CASR;;;EACA,IAAIK,KAAK,GAAG,EAAZ;;EACA,IAAIF,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAJ,EAAyC;IACvCC,KAAK,GAAG,EAAR;EACD,CAFD,MAEO;IACLA,KAAK,GAAGJ,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,UAAU9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAjC,CAAX,KAAuE,EAA/E;EACD;;EACD,MAAMC,aAAa,GAAGP,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,eAAvB,CAAX,KAAuD,EAA7E;EACA,IAAIK,OAAO,GAAG,gCAAd;;EACA,IAAIJ,KAAJ,EAAW;IACTI,OAAO,GAAGJ,KAAV;EACD,CAFD,MAEO,IAAIG,aAAJ,EAAmB;IACxBC,OAAO,GAAG,YAAYD,aAAtB;EACD,CAtBO,CAuBR;;;EACA,MAAME,UAAU,GAAGP,cAAc,CAAE,aAAYA,cAAc,CAACQ,KAAM,EAAnC,CAAd,IAAuD,EAA1E;;EACA,IAAID,UAAJ,EAAgB;IACd,MAAME,cAAc,GAAGX,IAAI,CAACC,KAAL,CAAWQ,UAAX,CAAvB;;IACA,IAAIb,MAAM,CAACE,GAAP,CAAWc,OAAX,CAAmBD,cAAnB,MAAuC,CAAC,CAA5C,EAA+C;MAC7Cf,MAAM,CAACL,OAAP,CAAesB,YAAf,GAA8Bb,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,gBAAgB9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAvC,CAAX,KAA6E,EAA3G;IACD;EACF;;EAED,IAAIQ,MAAM,GAAG,oEAAb;EACA,IAAIhB,GAAG,GAAGgB,MAAM,CAACC,IAAP,CAAYnB,MAAM,CAACE,GAAnB,CAAV;;EACA,IAAIA,GAAJ,EAAS;IACP,IAAIA,GAAG,CAAC,CAAD,CAAH,KAAW,gBAAf,EAAiC;MAC/B,IAAIF,MAAM,CAACoB,MAAP,KAAkB,MAAtB,EAA8B;QAC5B,IAAIC,MAAM,CAACZ,SAAP,CAAiBa,QAAjB,CAA0BC,IAA1B,CAA+BvB,MAAM,CAACwB,IAAtC,KAA+C,mBAAnD,EAAwE;UAAE;UACxE,IAAIxB,MAAM,CAACL,OAAP,CAAe,cAAf,MAAmC,gCAAvC,EAAyE;YACvEK,MAAM,CAACwB,IAAP,GAAc7C,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC1B,MAAM,CAACwB,IAAR,CAAnB,CAAd;UACD;QACF;MACF,CAND,MAMO,IAAIxB,MAAM,CAACoB,MAAP,KAAkB,KAAtB,EAA6B;QAClCpB,MAAM,CAAC2B,MAAP,GAAgBD,MAAM,CAAC1B,MAAM,CAAC2B,MAAR,CAAtB;MACD;;MACD,OAAO3B,MAAP;IACD;EACF,CA/CO,CAgDR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAA,MAAM,CAACL,OAAP,CAAeiC,YAAf,GAA8BvC,MAAM,CAACC,QAAP,CAAgBuC,MAAhB,KAA2B,8BAA3B,GAA4D,IAA5D,GAAmExC,MAAM,CAACC,QAAP,CAAgBuC,MAAhB,KAA2B,uBAA3B,GAAqD,IAArD,GAA4DxC,MAAM,CAACC,QAAP,CAAgBuC,MAAhB,KAA2B,sBAA3B,GAAoD,KAApD,GAA4D,MAAzN,CAlFQ,CAmFR;;EACA,MAAMC,MAAM,GAAG1B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAW9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAlC,CAAX,KAAwE,QAAvF;;EACA,IAAI,CAAC,CAACrB,MAAM,CAAC0C,aAAT,IAA0B,mBAAmB1C,MAAjD,EAAyD;IAAE;IACzD,IAAIW,MAAM,CAACoB,MAAP,KAAkB,MAAtB,EAA8B;MAC5B;MACA,IAAIC,MAAM,CAACZ,SAAP,CAAiBa,QAAjB,CAA0BC,IAA1B,CAA+BvB,MAAM,CAACwB,IAAtC,KAA+C,mBAAnD,EAAwE;QAAE;QACxExB,MAAM,CAACwB,IAAP,GAAc7C,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC;UAChCM,UAAU,EAAEpB,OADoB;UAEhCqB,WAAW,EAAEH,MAFmB;UAGhC,GAAG9B,MAAM,CAACwB;QAHsB,CAAD,CAAnB,CAAd;;QAKA,IAAIlB,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAJ,EAAyC;UACvC,IAAIP,MAAM,CAACE,GAAP,CAAWc,OAAX,CAAmB,GAAnB,MAA4B,CAAC,CAAjC,EAAoC;YAClChB,MAAM,CAACE,GAAP,IAAe,cAAagC,SAAS,CAAC9B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,EAAgD4B,SAAjD,CAA4D,EAAjG;UACD,CAFD,MAEO;YACLnC,MAAM,CAACE,GAAP,IAAe,cAAagC,SAAS,CAAC9B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,EAAgD4B,SAAjD,CAA4D,EAAjG;UACD;QACF;MACF,CAbD,MAaO;QACLnC,MAAM,CAACwB,IAAP,CAAYY,MAAZ,CAAmB,YAAnB,EAAiCxB,OAAjC;QACAZ,MAAM,CAACwB,IAAP,CAAYY,MAAZ,CAAmB,aAAnB,EAAkCN,MAAlC;MACD;IACF,CAnBD,MAmBO,IAAI9B,MAAM,CAACoB,MAAP,KAAkB,KAAtB,EAA6B;MAClCpB,MAAM,CAAC2B,MAAP,GAAgBD,MAAM,CAAC,EACrB,GAAG1B,MAAM,CAAC2B,MADW;QAErBK,UAAU,EAAEpB,OAFS;QAGrBqB,WAAW,EAAEH;MAHQ,CAAD,CAAtB;;MAKA,IAAI,CAAC9B,MAAM,CAAC2B,MAAZ,EAAoB;QAClB3B,MAAM,CAAC2B,MAAP,GAAgBhD,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC;UAClCM,UAAU,EAAEpB,OADsB;UAElCqB,WAAW,EAAEH;QAFqB,CAAD,CAAnB,CAAhB;MAID;IACF;;IACDO,OAAO,CAACC,GAAR,CAAYtC,MAAZ;EACD,CAlCD,MAkCO;IACLA,MAAM,CAACL,OAAP,CAAe4C,aAAf,GAA+B3B,OAA/B;IACAZ,MAAM,CAACL,OAAP,CAAe,gBAAf,IAAmCmC,MAAnC;;IACA,IAAIxB,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAJ,EAAyC;MACvCP,MAAM,CAACL,OAAP,CAAewC,SAAf,GAA2B/B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,EAAgD4B,SAA3E;IACD;;IACD,IAAInC,MAAM,CAACoB,MAAP,KAAkB,MAAtB,EAA8B;MAC5B,IAAIC,MAAM,CAACZ,SAAP,CAAiBa,QAAjB,CAA0BC,IAA1B,CAA+BvB,MAAM,CAACwB,IAAtC,KAA+C,mBAAnD,EAAwE;QAAE;QACxE,IAAIxB,MAAM,CAACL,OAAP,CAAe,cAAf,MAAmC,gCAAvC,EAAyE;UACvEK,MAAM,CAACwB,IAAP,GAAc7C,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC1B,MAAM,CAACwB,IAAR,CAAnB,CAAd;QACD;MACF;IACF,CAND,MAMO,IAAIxB,MAAM,CAACoB,MAAP,KAAkB,KAAtB,EAA6B;MAClCpB,MAAM,CAAC2B,MAAP,GAAgBD,MAAM,CAAC1B,MAAM,CAAC2B,MAAR,CAAtB;IACD;EACF;;EACD,OAAO3B,MAAP;AACD,CAzIH,EAyIKwC,KAAK,IAAI;EACV;EACA,OAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACD,CA5IH,E,CA8IA;;AACA9D,KAAK,CAACmB,YAAN,CAAmB8C,QAAnB,CAA4B5C,GAA5B,CACE4C,QAAQ,IAAI;EACV;EACA;EACA,IAAIC,IAAI,GAAGD,QAAQ,CAACnB,IAAT,CAAcqB,OAAd,IAAyBF,QAAQ,CAACnB,IAAT,CAAcoB,IAAlD;EACA,IAAIE,OAAO,GAAGH,QAAQ,CAACnB,IAAT,CAAcuB,MAAd,IAAwBJ,QAAQ,CAACnB,IAAT,CAAcsB,OAApD;;EACA,IAAIF,IAAI,KAAK,GAAb,EAAkB;IAChB,OAAOH,OAAO,CAACO,OAAR,CAAgBL,QAAhB,CAAP;EACD,CAFD,MAEO,IAAIC,IAAI,KAAK,GAAb,EAAkB;IACvB,SAAQJ,KAAR,CAAcM,OAAd;;IACAxC,cAAc,CAAC2C,KAAf;IACArE,MAAM,CAACsE,IAAP,CAAY;MACVC,IAAI,EAAE;IADI,CAAZ;IAGA,OAAOV,OAAO,CAACC,MAAR,CAAeC,QAAf,CAAP;EACD,CAPM,MAOA,IAAIC,IAAI,KAAK,GAAb,EAAkB;IACvB;IACA,SAAQJ,KAAR,CAAcM,OAAd;;IACA,OAAOL,OAAO,CAACC,MAAR,CAAeC,QAAf,CAAP;EACD,CAJM,MAIA,IAAIC,IAAI,KAAKQ,SAAb,EAAwB;IAAE;IAC/B,OAAOX,OAAO,CAACO,OAAR,CAAgBL,QAAhB,CAAP;EACD,CAFM,MAEA;IACL,SAAQ;MACNG,OAAO,EAAEA,OAAO,IAAI,MADd;MAENO,IAAI,EAAE;IAFA,CAAR;;IAIA,OAAOZ,OAAO,CAACC,MAAR,CAAeC,QAAf,CAAP;EACD;AACF,CA5BH,EA4BKH,KAAK,IAAI;EACV,IAAIA,KAAK,IAAIA,KAAK,CAACM,OAAN,CAAcQ,QAAd,CAAuB,SAAvB,CAAb,EAAgD;IAC9C,SAAQd,KAAR,CAAc,aAAd;;IACA,OAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;EACD;;EACDH,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BE,KAA1B,EALU,CAMV;EACA;EACA;EACA;;EACA,OAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACD,CAvCH;;AA0CA,SAASd,MAAT,CAAiB6B,KAAjB,EAAwB;EAAE;EACxB,IAAI/B,IAAI,GAAG+B,KAAX;;EACA,KAAK,IAAIC,GAAT,IAAgBhC,IAAhB,EAAsB;IACpB,IAAIA,IAAI,CAACgC,GAAD,CAAJ,KAAc,IAAlB,EAAwB;MACtB,OAAOhC,IAAI,CAACgC,GAAD,CAAX;IACD;EACF;;EACD,OAAOhC,IAAP;AACD,C,CACD;;;AACA,OAAO,MAAMiC,GAAG,GAAG,CAACvD,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EAC3C,OAAO,IAAI8C,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAAC+E,GAAN,CAAUvD,GAAV,EAAe;MACbP,OAAO,EAAEA,OADI;MAEbgC,MAAM,EAAEA;IAFK,CAAf,EAGG+B,IAHH,CAGQC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CALD,EAKGoC,KALH,CAKSC,GAAG,IAAI;MACdnB,MAAM,CAACmB,GAAD,CAAN;IACD,CAPD;EAQD,CATM,CAAP;AAUD,CAXM,C,CAYP;;AACA,OAAO,MAAMjE,IAAI,GAAG,CAACM,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EAC5C,OAAO,IAAI8C,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgBwB,MAAM,CAACC,MAAD,CAAtB,EAAgC;MAC9BhC,OAAO,EAAEA;IADqB,CAAhC,EAGG+D,IAHH,CAGQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CALH,EAMGoC,KANH,CAMSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARH;EASD,CAVM,CAAP;AAWD,CAZM,C,CAaP;;AACA,OAAO,MAAMC,QAAQ,GAAG,CAAC5D,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EAChD,OAAO,IAAI8C,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwB;MACtBhC,OAAO,EAAEA;IADa,CAAxB,EAGG+D,IAHH,CAGQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CALH,EAMGoC,KANH,CAMSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARH;EASD,CAVM,CAAP;AAWD,CAZM,C,CAaP;;AACA,OAAO,MAAME,YAAY,GAAG,CAAC7D,GAAD,EAAMyB,MAAN,EAAcqC,OAAd,KAA0B;EACpD,OAAO,IAAIvB,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwBqC,OAAxB,EACGN,IADH,CACQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CAHH,EAIGoC,KAJH,CAISC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CANH;EAOD,CARM,CAAP;AASD,CAVM,C,CAWP;;AACA,OAAO,MAAMI,gBAAgB,GAAG,CAAC/D,GAAD,EAAMyB,MAAN,EAAcjC,OAAd,EAAuBwE,QAAvB,EAAiCC,EAAjC,KAAwC;EACtE,OAAO,IAAI1B,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAAC;MACJwB,GADI;MAEJkB,MAAM,EAAE,MAFJ;MAGJI,IAAI,EAAEG,MAHF;MAIJjC,OAJI;MAKJ0E,gBAAgB,EAAEC,CAAC,IAAI;QACrBH,QAAQ,CAACG,CAAD,EAAIF,EAAJ,CAAR;MACD;IAPG,CAAD,CAAL,CASGT,IATH,CASQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CAXH,EAYGoC,KAZH,CAYSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CAdH;EAeD,CAhBM,CAAP;AAiBD,CAlBM;AAmBP;;AACA,OAAO,MAAMS,YAAY,GAAG,CAACpE,GAAD,EAAMyB,MAAN,EAAc0B,IAAI,GAAG,MAArB,KAAgC;EAC1D,OAAO,IAAIZ,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAAC;MACJ0C,MAAM,EAAE,MADJ;MAEJlB,GAAG,EAAEA,GAFD;MAGJsB,IAAI,EAAEG,MAHF;MAIJ4C,YAAY,EAAElB;IAJV,CAAD,CAAL,CAMGK,IANH,CAMQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CARH,EASGoC,KATH,CASSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CAXH;EAYD,CAbM,CAAP;AAcD,CAfM,C,CAgBP;;AACA,OAAO,MAAMW,WAAW,GAAG,CAACtE,GAAD,EAAMyB,MAAN,EAAc8C,IAAd,KAAuB;EAChD,OAAO/F,KAAK,CAAC;IACX0C,MAAM,EAAE,MADG;IAEXlB,GAAG,EAAEA,GAFM;IAGXsB,IAAI,EAAEG,MAHK;IAIX4C,YAAY,EAAE;EAJH,CAAD,CAAL,CAKJb,IALI,CAKCC,GAAG,IAAI;IACb,MAAMe,OAAO,GAAGf,GAAG,CAACnC,IAApB;IACA,MAAMmD,IAAI,GAAG,IAAIC,IAAJ,CAAS,CAACF,OAAD,CAAT,CAAb;IACA,MAAMG,QAAQ,GAAGJ,IAAjB;;IACA,IAAI,cAAcK,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAlB,EAA+C;MAAE;MAC/C,MAAMC,KAAK,GAAGF,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAd;MACAC,KAAK,CAACC,QAAN,GAAiBJ,QAAjB;MACAG,KAAK,CAACE,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;MACAH,KAAK,CAACI,IAAN,GAAaC,GAAG,CAACC,eAAJ,CAAoBX,IAApB,CAAb;MACAG,QAAQ,CAACS,IAAT,CAAcC,WAAd,CAA0BR,KAA1B;MACAA,KAAK,CAACS,KAAN;MACAJ,GAAG,CAACK,eAAJ,CAAoBV,KAAK,CAACI,IAA1B,EAP6C,CAOb;;MAChCN,QAAQ,CAACS,IAAT,CAAcI,WAAd,CAA0BX,KAA1B;IACD,CATD,MASO;MAAE;MACPY,SAAS,CAACC,UAAV,CAAqBlB,IAArB,EAA2BE,QAA3B;IACD;EACF,CArBM,CAAP;AAsBD,CAvBM,C,CAwBP;;AACA,OAAO,MAAMiB,UAAU,GAAG,CAAC5F,GAAD,EAAMyB,MAAN,KAAiB;EACzC,IAAIoE,MAAM,GAAGjB,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAb;EACAgB,MAAM,CAAC3E,MAAP,GAAgB,MAAhB;EACA,MAAMjB,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,YAAvB,CAAX,KAAoD,EAAvE;EACA,IAAIyF,WAAW,GAAGlH,OAAlB;;EACA,IAAIqB,UAAJ,EAAgB;IACd6F,WAAW,GAAG7F,UAAd;EACD;;EACD4F,MAAM,CAACE,MAAP,GAAiB,GAAED,WAAY,GAAE9F,GAAI,EAArC;EACA4E,QAAQ,CAACS,IAAT,CAAcC,WAAd,CAA0BO,MAA1B;EACA,MAAMvF,KAAK,GAAGF,cAAc,CAACC,OAAf,CAAuB,UAAU9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAjC,KAA2D,EAAzE;EACA,IAAIE,OAAO,GAAG,gCAAd;;EACA,IAAIJ,KAAJ,EAAW;IACTI,OAAO,GAAGR,IAAI,CAACC,KAAL,CAAWG,KAAX,CAAV;EACD;;EACD,MAAMsB,MAAM,GAAG1B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAW9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAlC,CAAX,KAAwE,EAAvF;EACA,IAAIsB,UAAU,GAAG8C,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAjB;EACA/C,UAAU,CAACkE,YAAX,CAAwB,MAAxB,EAAgC,YAAhC;EACAlE,UAAU,CAACkE,YAAX,CAAwB,OAAxB,EAAiCtF,OAAjC;EACAmF,MAAM,CAACP,WAAP,CAAmBxD,UAAnB;EACA,IAAIC,WAAW,GAAG6C,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAlB;EACA9C,WAAW,CAACiE,YAAZ,CAAyB,MAAzB,EAAiC,aAAjC;EACAjE,WAAW,CAACiE,YAAZ,CAAyB,OAAzB,EAAkCpE,MAAlC;EACAiE,MAAM,CAACP,WAAP,CAAmBvD,WAAnB;;EACA,KAAK,MAAMuB,GAAX,IAAkB7B,MAAlB,EAA0B;IACxB,IAAIwB,IAAI,GAAG,UAAUK,GAArB;IACAnE,MAAM,CAAC8D,IAAD,CAAN,GAAe2B,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAf;IACA1F,MAAM,CAAC8D,IAAD,CAAN,CAAa+C,YAAb,CAA0B,MAA1B,EAAkC1C,GAAlC;IACAnE,MAAM,CAAC8D,IAAD,CAAN,CAAa+C,YAAb,CAA0B,OAA1B,EAAmCvE,MAAM,CAAC6B,GAAD,CAAzC;IACAuC,MAAM,CAACP,WAAP,CAAmBnG,MAAM,CAAC8D,IAAD,CAAzB;EACD;;EACD4C,MAAM,CAACI,MAAP;EACArB,QAAQ,CAACS,IAAT,CAAcI,WAAd,CAA0BI,MAA1B;AACD,CAjCM,C,CAkCP;;AACA,OAAO,MAAMK,KAAK,GAAG,CAAClG,GAAD,EAAMyB,MAAN,KAAiB;EACpC,OAAO,IAAIc,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwB;MACtBhC,OAAO,EAAE;QACP,gBAAgB;MADT;IADa,CAAxB,EAIG+D,IAJH,CAIQC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CAND,EAMGoC,KANH,CAMSC,GAAG,IAAI;MACdnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARD;EASD,CAVM,CAAP;AAWD,CAZM;;AAaP,MAAMwC,QAAQ,GAAG,CAACnG,GAAD,EAAMyB,MAAN,KAAiB;EAChC,OAAO,IAAIc,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgBvB,EAAE,CAAC8C,SAAH,CAAaE,MAAb,CAAhB,EAAsC;MACpChC,OAAO,EAAE;QACP,gBAAgB;MADT;IAD2B,CAAtC,EAIG+D,IAJH,CAIQC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAD,CAAP;IACD,CAND,EAMGC,KANH,CAMSC,GAAG,IAAI;MACdnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARD;EASD,CAVM,CAAP;AAWD,CAZD;;AAcA,SAASyC,UAAT,CAAqB3E,MAArB,EAA6B;EAC3B,MAAMH,IAAI,GAAG,IAAI+E,QAAJ,EAAb;;EACA,KAAK,IAAIC,CAAT,IAAc7E,MAAd,EAAsB;IACpBH,IAAI,CAACY,MAAL,CAAYoE,CAAZ,EAAe7E,MAAM,CAAC6E,CAAD,CAArB;EACD;;EACD,OAAOhF,IAAP;AACD;;AACD,MAAMiF,QAAQ,GAAG,CAACvG,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EACzCgC,MAAM,GAAG2E,UAAU,CAAC3E,MAAD,CAAnB;EACA,OAAO,IAAIc,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwB;MACtBhC,OAAO,EAAEA;IADa,CAAxB,EAGG+D,IAHH,CAGQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACnC,IAAL,CAAP;IACD,CALH,EAMGoC,KANH,CAMSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARH;EASD,CAVM,CAAP;AAWD,CAbD;;AAcA,MAAM6C,WAAW,GAAG,CAACxG,GAAD,EAAMyB,MAAN,EAAckD,QAAd,KAA2B;EAC7C,OAAO,IAAIpC,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtChE,KAAK,CAAC;MACJ0C,MAAM,EAAE,MADJ;MAEJlB,GAAG,EAAEA,GAFD;MAGJsB,IAAI,EAAEG,MAHF;MAIJ4C,YAAY,EAAE;IAJV,CAAD,CAAL,CAKGb,IALH,CAKQC,GAAG,IAAI;MACb,MAAMgB,IAAI,GAAG,IAAIC,IAAJ,CAAS,CAACjB,GAAG,CAAC7D,OAAJ,CAAY6C,QAAb,CAAT,CAAb;MACA,MAAMqC,KAAK,GAAGF,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAd;MACAC,KAAK,CAACC,QAAN,GAAiBJ,QAAjB;MACAG,KAAK,CAACE,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;MACAH,KAAK,CAACI,IAAN,GAAaC,GAAG,CAACC,eAAJ,CAAoBX,IAApB,CAAb;MACAG,QAAQ,CAACS,IAAT,CAAcC,WAAd,CAA0BR,KAA1B;MACAA,KAAK,CAACS,KAAN;MACAX,QAAQ,CAACS,IAAT,CAAcI,WAAd,CAA0BX,KAA1B;;MACA,SAAQ2B,OAAR,CAAgB,MAAhB;;MACA3D,OAAO,CAACW,GAAD,CAAP;IACD,CAhBD,EAgBGC,KAhBH,CAgBSC,GAAG,IAAI;MACd,SAAQrB,KAAR,CAAc,MAAd;;MACAE,MAAM,CAACmB,GAAD,CAAN;IACD,CAnBD;EAoBD,CArBM,CAAP;AAsBD,CAvBD;;AAwBA,eAAe;EACbJ,GADa;EAEb7D,IAAI,EAAEwG,KAFO;EAGbC,QAHa;EAIbI,QAJa;EAKbG,MAAM,EAAE9C,QALK;EAMb+C,MAAM,EAAEH;AANK,CAAf"}]}